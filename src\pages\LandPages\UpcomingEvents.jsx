"use client"

import { useState, useEffect, useRef } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { useNavigate } from "react-router-dom"
import { useRegisterEventMutation, useCheckPaymentStatusQuery } from "./upcomingEvents.Slice"

const RegistrationForm = () => {
  const navigate = useNavigate()
  const paymentSectionRef = useRef(null)

  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    age: "",
    gender: "",
    organization: "",
    city: "",
    state: "",
  })

  const [errors, setErrors] = useState({})
  const [registerEvent, { isLoading: isRegistering, error: registerError }] = useRegisterEventMutation()
  const [paymentId, setPaymentId] = useState(null)
  const [qrError, setQrError] = useState(null)
  const [registrationData, setRegistrationData] = useState(null)
  const [paymentStatus, setPaymentStatus] = useState(null)
  const [showSuccessModal, setShowSuccessModal] = useState(false)

  const {
    data: statusData,
    isFetching: isCheckingStatus,
    error: statusError,
  } = useCheckPaymentStatusQuery(paymentId, {
    skip: !paymentId,
    pollingInterval: 10000,
  })

  // Auto-scroll to payment section when QR code appears
  useEffect(() => {
    if (registrationData && registrationData.payment_url && paymentSectionRef.current) {
      setTimeout(() => {
        paymentSectionRef.current.scrollIntoView({
          behavior: "smooth",
          block: "center",
        })
      }, 500) // Small delay to ensure the section is rendered
    }
  }, [registrationData])

  // Handle payment status updates
  useEffect(() => {
    if (statusData) {
      const status = statusData.status?.toLowerCase()
      const paidAt = statusData.paid_at || null
      setPaymentStatus({ status, paidAt })
      if (status === "paid") {
        setShowSuccessModal(true)
      }
    }
    if (statusError) {
      setErrors({ api: statusError.data?.error || "Failed to check payment status" })
    }
  }, [statusData, statusError])

  const validateForm = () => {
    const newErrors = {}
    if (!formData.name.trim()) newErrors.name = "Name is required"
    if (!formData.email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = "Valid email is required"
    }
    if (!formData.phone || !/^\d{10}$/.test(formData.phone)) {
      newErrors.phone = "Phone must be exactly 10 digits"
    }
    if (!formData.age || isNaN(formData.age) || formData.age <= 0 || formData.age > 120) {
      newErrors.age = "Valid age is required (1-120)"
    }
    if (!formData.gender) newErrors.gender = "Gender is required"
    if (!formData.organization.trim()) newErrors.organization = "Organization is required"
    if (!formData.city.trim()) newErrors.city = "City is required"
    if (!formData.state.trim()) newErrors.state = "State is required"

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleInputChange = (e) => {
    const { name, value } = e.target

    // Clear specific error when user starts typing
    if (errors[name]) {
      setErrors((prev) => ({ ...prev, [name]: null }))
    }

    if (name === "phone") {
      if (/^\d{0,10}$/.test(value)) {
        setFormData({ ...formData, [name]: value })
      }
    } else if (name === "age") {
      if (/^\d*$/.test(value) && (value === "" || Number.parseInt(value) <= 120)) {
        setFormData({ ...formData, [name]: value })
      }
    } else {
      setFormData({ ...formData, [name]: value })
    }
  }

  const handleSubmit = async () => {
    if (!validateForm()) {
      // Scroll to first error
      const firstErrorField = Object.keys(errors)[0]
      const errorElement = document.querySelector(`[name="${firstErrorField}"]`)
      if (errorElement) {
        errorElement.scrollIntoView({ behavior: "smooth", block: "center" })
        errorElement.focus()
      }
      return
    }

    try {
      const result = await registerEvent(formData).unwrap()
      console.log("Registration successful:", result)

      setRegistrationData({
        payment_id: result.payment_id,
        payment_url: result.payment_url,
        qr_base64: result.qr_base64,
      })
      setPaymentId(result.payment_id)
      setErrors({})
      setQrError(null)
    } catch (err) {
      setErrors({ api: err.data?.error || "Registration failed. Please try again." })
    }
  }

  const handleModalClose = () => {
    setShowSuccessModal(false)
    setFormData({
      name: "",
      email: "",
      phone: "",
      age: "",
      gender: "",
      organization: "",
      city: "",
      state: "",
    })
    setPaymentId(null)
    setQrError(null)
    setRegistrationData(null)
    setPaymentStatus(null)
    navigate("/")
  }

  const containerVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
        staggerChildren: 0.1,
        ease: "easeOut",
      },
    },
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5, ease: "easeOut" },
    },
  }

  const inputVariants = {
    focus: {
      scale: 1.02,
      boxShadow: "0 0 0 3px rgba(59, 130, 246, 0.1)",
      transition: { duration: 0.2 },
    },
    blur: {
      scale: 1,
      boxShadow: "0 0 0 0px rgba(59, 130, 246, 0)",
      transition: { duration: 0.2 },
    },
  }

  const paymentVariants = {
    hidden: { opacity: 0, y: 50, scale: 0.9 },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        duration: 0.6,
        ease: "easeOut",
        staggerChildren: 0.2,
      },
    },
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-cyan-50 py-8 px-4">
      <div className="max-w-4xl mx-auto">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="bg-white/90 backdrop-blur-lg p-8 md:p-12 rounded-3xl shadow-2xl border border-white/30"
        >
          {/* Header Section */}
          <motion.div variants={itemVariants} className="text-center mb-10">
            <motion.div
              className="w-20 h-20 bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 rounded-2xl mx-auto mb-6 flex items-center justify-center shadow-lg"
              whileHover={{ rotate: 5, scale: 1.05 }}
              transition={{ duration: 0.3 }}
            >
              <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                />
              </svg>
            </motion.div>
            <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 bg-clip-text text-transparent mb-3">
              Event Registration
            </h1>
            <p className="text-gray-600 text-lg">Join us for an amazing experience ✨</p>
          </motion.div>

          {/* Form Section */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            {/* Full Name */}
            <motion.div variants={itemVariants} className="lg:col-span-2">
              <label className="block text-gray-800 font-semibold mb-3 text-sm uppercase tracking-wide">
                Full Name *
              </label>
              <motion.input
                variants={inputVariants}
                whileFocus="focus"
                type="text"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                className={`w-full p-4 border-2 rounded-2xl focus:outline-none transition-all duration-300 bg-gray-50/50 ${
                  errors.name ? "border-red-300 focus:border-red-500" : "border-gray-200 focus:border-indigo-500"
                }`}
                placeholder="Enter your full name"
              />
              <AnimatePresence>
                {errors.name && (
                  <motion.p
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="text-red-500 text-sm mt-2 flex items-center"
                  >
                    <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path
                        fillRule="evenodd"
                        d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                        clipRule="evenodd"
                      />
                    </svg>
                    {errors.name}
                  </motion.p>
                )}
              </AnimatePresence>
            </motion.div>

            {/* Email */}
            <motion.div variants={itemVariants}>
              <label className="block text-gray-800 font-semibold mb-3 text-sm uppercase tracking-wide">
                Email Address *
              </label>
              <motion.input
                variants={inputVariants}
                whileFocus="focus"
                type="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                className={`w-full p-4 border-2 rounded-2xl focus:outline-none transition-all duration-300 bg-gray-50/50 ${
                  errors.email ? "border-red-300 focus:border-red-500" : "border-gray-200 focus:border-indigo-500"
                }`}
                placeholder="<EMAIL>"
              />
              <AnimatePresence>
                {errors.email && (
                  <motion.p
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="text-red-500 text-sm mt-2 flex items-center"
                  >
                    <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path
                        fillRule="evenodd"
                        d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                        clipRule="evenodd"
                      />
                    </svg>
                    {errors.email}
                  </motion.p>
                )}
              </AnimatePresence>
            </motion.div>

            {/* Phone */}
            <motion.div variants={itemVariants}>
              <label className="block text-gray-800 font-semibold mb-3 text-sm uppercase tracking-wide">
                Phone Number *
              </label>
              <motion.input
                variants={inputVariants}
                whileFocus="focus"
                type="text"
                name="phone"
                value={formData.phone}
                onChange={handleInputChange}
                className={`w-full p-4 border-2 rounded-2xl focus:outline-none transition-all duration-300 bg-gray-50/50 ${
                  errors.phone ? "border-red-300 focus:border-red-500" : "border-gray-200 focus:border-indigo-500"
                }`}
                placeholder="10-digit phone number"
              />
              <AnimatePresence>
                {errors.phone && (
                  <motion.p
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="text-red-500 text-sm mt-2 flex items-center"
                  >
                    <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path
                        fillRule="evenodd"
                        d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                        clipRule="evenodd"
                      />
                    </svg>
                    {errors.phone}
                  </motion.p>
                )}
              </AnimatePresence>
            </motion.div>

            {/* Age */}
            <motion.div variants={itemVariants}>
              <label className="block text-gray-800 font-semibold mb-3 text-sm uppercase tracking-wide">Age *</label>
              <motion.input
                variants={inputVariants}
                whileFocus="focus"
                type="number"
                name="age"
                value={formData.age}
                onChange={handleInputChange}
                className={`w-full p-4 border-2 rounded-2xl focus:outline-none transition-all duration-300 bg-gray-50/50 ${
                  errors.age ? "border-red-300 focus:border-red-500" : "border-gray-200 focus:border-indigo-500"
                }`}
                placeholder="Your age"
                min="1"
                max="120"
              />
              <AnimatePresence>
                {errors.age && (
                  <motion.p
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="text-red-500 text-sm mt-2 flex items-center"
                  >
                    <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path
                        fillRule="evenodd"
                        d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                        clipRule="evenodd"
                      />
                    </svg>
                    {errors.age}
                  </motion.p>
                )}
              </AnimatePresence>
            </motion.div>

            {/* Gender */}
            <motion.div variants={itemVariants}>
              <label className="block text-gray-800 font-semibold mb-3 text-sm uppercase tracking-wide">Gender *</label>
              <motion.select
                variants={inputVariants}
                whileFocus="focus"
                name="gender"
                value={formData.gender}
                onChange={handleInputChange}
                className={`w-full p-4 border-2 rounded-2xl focus:outline-none transition-all duration-300 bg-gray-50/50 ${
                  errors.gender ? "border-red-300 focus:border-red-500" : "border-gray-200 focus:border-indigo-500"
                }`}
              >
                <option value="">Select Gender</option>
                <option value="Male">Male</option>
                <option value="Female">Female</option>
                <option value="Other">Other</option>
              </motion.select>
              <AnimatePresence>
                {errors.gender && (
                  <motion.p
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="text-red-500 text-sm mt-2 flex items-center"
                  >
                    <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path
                        fillRule="evenodd"
                        d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                        clipRule="evenodd"
                      />
                    </svg>
                    {errors.gender}
                  </motion.p>
                )}
              </AnimatePresence>
            </motion.div>

            {/* Organization */}
            <motion.div variants={itemVariants} className="lg:col-span-2">
              <label className="block text-gray-800 font-semibold mb-3 text-sm uppercase tracking-wide">
                Organization *
              </label>
              <motion.input
                variants={inputVariants}
                whileFocus="focus"
                type="text"
                name="organization"
                value={formData.organization}
                onChange={handleInputChange}
                className={`w-full p-4 border-2 rounded-2xl focus:outline-none transition-all duration-300 bg-gray-50/50 ${
                  errors.organization
                    ? "border-red-300 focus:border-red-500"
                    : "border-gray-200 focus:border-indigo-500"
                }`}
                placeholder="Your organization or company"
              />
              <AnimatePresence>
                {errors.organization && (
                  <motion.p
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="text-red-500 text-sm mt-2 flex items-center"
                  >
                    <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path
                        fillRule="evenodd"
                        d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                        clipRule="evenodd"
                      />
                    </svg>
                    {errors.organization}
                  </motion.p>
                )}
              </AnimatePresence>
            </motion.div>

            {/* City */}
            <motion.div variants={itemVariants}>
              <label className="block text-gray-800 font-semibold mb-3 text-sm uppercase tracking-wide">City *</label>
              <motion.input
                variants={inputVariants}
                whileFocus="focus"
                type="text"
                name="city"
                value={formData.city}
                onChange={handleInputChange}
                className={`w-full p-4 border-2 rounded-2xl focus:outline-none transition-all duration-300 bg-gray-50/50 ${
                  errors.city ? "border-red-300 focus:border-red-500" : "border-gray-200 focus:border-indigo-500"
                }`}
                placeholder="Your city"
              />
              <AnimatePresence>
                {errors.city && (
                  <motion.p
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="text-red-500 text-sm mt-2 flex items-center"
                  >
                    <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path
                        fillRule="evenodd"
                        d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                        clipRule="evenodd"
                      />
                    </svg>
                    {errors.city}
                  </motion.p>
                )}
              </AnimatePresence>
            </motion.div>

            {/* State */}
            <motion.div variants={itemVariants}>
              <label className="block text-gray-800 font-semibold mb-3 text-sm uppercase tracking-wide">State *</label>
              <motion.input
                variants={inputVariants}
                whileFocus="focus"
                type="text"
                name="state"
                value={formData.state}
                onChange={handleInputChange}
                className={`w-full p-4 border-2 rounded-2xl focus:outline-none transition-all duration-300 bg-gray-50/50 ${
                  errors.state ? "border-red-300 focus:border-red-500" : "border-gray-200 focus:border-indigo-500"
                }`}
                placeholder="Your state"
              />
              <AnimatePresence>
                {errors.state && (
                  <motion.p
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="text-red-500 text-sm mt-2 flex items-center"
                  >
                    <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path
                        fillRule="evenodd"
                        d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                        clipRule="evenodd"
                      />
                    </svg>
                    {errors.state}
                  </motion.p>
                )}
              </AnimatePresence>
            </motion.div>
          </div>

          {/* API Error */}
          <AnimatePresence>
            {errors.api && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                className="mb-8 p-4 bg-red-50 border-l-4 border-red-400 rounded-r-xl"
              >
                <div className="flex items-center">
                  <svg className="w-6 h-6 text-red-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path
                      fillRule="evenodd"
                      d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                      clipRule="evenodd"
                    />
                  </svg>
                  <p className="text-red-700 font-medium">{errors.api}</p>
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Submit Button */}
          <motion.button
            variants={itemVariants}
            whileHover={{ scale: 1.02, y: -2 }}
            whileTap={{ scale: 0.98 }}
            onClick={handleSubmit}
            disabled={isRegistering}
            className="w-full bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white p-5 rounded-2xl font-bold text-lg shadow-xl hover:shadow-2xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed relative overflow-hidden"
          >
            <motion.div className="absolute inset-0 bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 opacity-0 hover:opacity-100 transition-opacity duration-300" />
            <span className="relative z-10">
              {isRegistering ? (
                <span className="flex items-center justify-center">
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 1, repeat: Number.POSITIVE_INFINITY, ease: "linear" }}
                    className="w-6 h-6 border-2 border-white border-t-transparent rounded-full mr-3"
                  />
                  Registering...
                </span>
              ) : (
                <span className="flex items-center justify-center">
                  <svg className="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                  Register for Event
                </span>
              )}
            </span>
          </motion.button>
        </motion.div>

        {/* Payment Section */}
        <AnimatePresence>
          {registrationData && registrationData.payment_url && (
            <motion.div
              ref={paymentSectionRef}
              variants={paymentVariants}
              initial="hidden"
              animate="visible"
              exit="hidden"
              className="mt-8 bg-gradient-to-br from-emerald-50 via-teal-50 to-cyan-50 p-8 md:p-12 rounded-3xl shadow-2xl border border-emerald-200"
            >
              <div className="text-center">
                <motion.div
                  className="w-16 h-16 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-2xl mx-auto mb-6 flex items-center justify-center shadow-lg"
                  animate={{
                    scale: [1, 1.1, 1],
                    rotate: [0, 5, -5, 0],
                  }}
                  transition={{
                    duration: 2,
                    repeat: Number.POSITIVE_INFINITY,
                    repeatType: "reverse",
                  }}
                >
                  <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"
                    />
                  </svg>
                </motion.div>

                <motion.h3 className="text-3xl font-bold text-gray-800 mb-4 pt-4" variants={itemVariants}>
                  🎉 Complete Your Payment
                </motion.h3>

                <motion.p className="text-gray-600 text-lg mb-8 max-w-md mx-auto py-2" variants={itemVariants}>
                  You're almost there! Complete your payment of ₹5000 to secure your spot at the event.
                  <motion.a
                  variants={itemVariants}
                  whileHover={{ scale: 1.05, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                  href={registrationData.payment_url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center px-8 py-4 my-6 bg-gradient-to-r from-emerald-500 to-teal-500 text-white rounded-2xl font-bold text-lg hover:shadow-xl transition-all duration-300 mb-8"
                >
                  <svg className="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
                    />
                  </svg>
                  Pay Now - ₹5000
                </motion.a>
                </motion.p>

                

                {registrationData.qr_base64 ? (
                  <motion.div
                    variants={itemVariants}
                    className="bg-white p-6 rounded-3xl shadow-xl inline-block border-4 border-emerald-100"
                  >
                    <motion.img
                      src={`data:image/png;base64,${registrationData.qr_base64}`}
                      alt="Payment QR Code"
                      className="w-48 h-48 mx-auto"
                      onError={() => setQrError("Failed to load QR code. Please use the payment link.")}
                      whileHover={{ scale: 1.05 }}
                      transition={{ duration: 0.3 }}
                    />
                    {qrError ? (
                      <motion.p
                        className="text-red-500 text-sm mt-4 font-medium"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                      >
                        {qrError}
                      </motion.p>
                    ) : (
                      <motion.p className="text-gray-600 mt-4 font-medium" variants={itemVariants}>
                        📱 Scan with any UPI app
                      </motion.p>
                    )}
                  </motion.div>
                ) : (
                  <motion.p className="text-red-500 font-medium" variants={itemVariants}>
                    QR code not available. Please use the payment link above.
                  </motion.p>
                )}

                {/* Payment Status Indicator */}
                {isCheckingStatus && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="mt-8 p-4 bg-yellow-100 border border-yellow-300 rounded-2xl"
                  >
                    <div className="flex items-center justify-center">
                      <motion.div
                        animate={{ rotate: 360 }}
                        transition={{ duration: 1, repeat: Number.POSITIVE_INFINITY, ease: "linear" }}
                        className="w-5 h-5 border-2 border-yellow-500 border-t-transparent rounded-full mr-3"
                      />
                      <p className="text-yellow-700 font-medium">Checking payment status...</p>
                    </div>
                  </motion.div>
                )}
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Payment Status */}
        <AnimatePresence>
          {paymentStatus && !showSuccessModal && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="mt-8 p-6 bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200 rounded-2xl text-center shadow-lg"
            >
              <div className="flex items-center justify-center mb-2">
                <svg className="w-6 h-6 text-yellow-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
                <p className="text-yellow-800 font-bold text-lg">
                  Payment Status: {paymentStatus.status.toUpperCase()}
                </p>
              </div>
              {paymentStatus.paidAt && (
                <p className="text-yellow-700">Paid At: {new Date(paymentStatus.paidAt).toLocaleString()}</p>
              )}
            </motion.div>
          )}
        </AnimatePresence>

        {/* Success Modal */}
        <AnimatePresence>
          {showSuccessModal && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4"
            >
              <motion.div
                initial={{ scale: 0.7, opacity: 0, y: 50 }}
                animate={{ scale: 1, opacity: 1, y: 0 }}
                exit={{ scale: 0.7, opacity: 0, y: 50 }}
                transition={{ type: "spring", duration: 0.6 }}
                className="bg-white p-10 rounded-3xl shadow-2xl max-w-md w-full text-center relative overflow-hidden"
              >
                {/* Confetti Background */}
                <div className="absolute inset-0 bg-gradient-to-br from-green-50 via-blue-50 to-purple-50 opacity-50" />

                <motion.div
                  initial={{ scale: 0, rotate: -180 }}
                  animate={{ scale: 1, rotate: 0 }}
                  transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
                  className="w-24 h-24 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full mx-auto mb-6 flex items-center justify-center shadow-xl relative z-10"
                >
                  <svg className="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </motion.div>

                <motion.h3
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3 }}
                  className="text-3xl font-bold text-gray-800 mb-4 relative z-10"
                >
                  🎉 Success!
                </motion.h3>

                <motion.p
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.4 }}
                  className="text-gray-600 mb-8 text-lg relative z-10"
                >
                  Congratulations! You have successfully registered for the event. A confirmation email has been sent to
                  your inbox.
                </motion.p>

                <motion.button
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.5 }}
                  whileHover={{ scale: 1.05, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={handleModalClose}
                  className="bg-gradient-to-r from-green-500 to-emerald-500 text-white px-10 py-4 rounded-2xl font-bold text-lg hover:shadow-xl transition-all duration-300 relative z-10"
                >
                  Close
                </motion.button>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  )
}

export default RegistrationForm
