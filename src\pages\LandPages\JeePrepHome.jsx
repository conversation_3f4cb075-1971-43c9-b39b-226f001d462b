import { motion, useScroll, useTransform } from 'framer-motion';
import { useRef } from 'react';
import { useNavigate } from 'react-router';
import student from '../../assets/indian.png';

const JeePrepHome = () => {
  const navigate = useNavigate();
  const containerRef = useRef(null);
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ['start start', 'end end']
  });

  // Enhanced parallax effects
  const yBg = useTransform(scrollYProgress, [0, 1], ['0%', '25%']);
  const yText = useTransform(scrollYProgress, [0, 1], ['0%', '40%']);
  const rotate = useTransform(scrollYProgress, [0, 1], [0, 3]);

  // Indian color palette
  const colors = {
    saffron: '#FF9933',
    white: '#FFFFFF',
    green: '#138808',
    navy: '#000080',
    gold: '#FFD700'
  };

  return (
    <div ref={containerRef} className="overflow-hidden bg-gradient-to-b from-gray-50 to-gray-100">
      {/* Back Button with Indian Motif */}
      <motion.button
        onClick={() => navigate('/')}
        initial={{ opacity: 0, x: -20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ delay: 0.5 }}
        className="fixed top-1 left-6 z-50 flex items-center gap-2 hover:cursor-pointer bg-white/90 px-4 py-2 rounded-full border border-gray-200 shadow-md"
        whileHover={{ scale: 1.05, backgroundColor: colors.saffron, color: colors.navy }}
        whileTap={{ scale: 0.95 }}>
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M10 19l-7-7m0 0l7-7m-7 7h18"
          />
        </svg>
        <span className="font-medium">Back</span>
      </motion.button>

      {/* Hero Section with Indian Classroom Parallax */}
      <section className="h-screen relative overflow-hidden">
        <motion.div
          style={{ y: yBg, rotate }}
          className="absolute inset-0 bg-[url('https://t3.ftcdn.net/jpg/13/26/88/34/360_F_1326883426_j4XyJNJb1YVupI0zx7LGMlYoHIDeesQY.jpg')] bg-cover bg-center">
          <div className="absolute inset-0 bg-gradient-to-t from-gray-800 via-gray-700/70 to-transparent">
            <div className="absolute bottom-0 left-0 w-full h-32 bg-gradient-to-t from-gray-800 to-transparent"></div>
          </div>
        </motion.div>

        <motion.div
          style={{ y: yText }}
          className="relative z-10 h-full flex flex-col justify-center items-center text-center px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="max-w-4xl mx-auto">
            <motion.div whileHover={{ scale: 1.1 }} className="mb-6 flex justify-center">
              <div
                className="px-4 py-1 rounded-full hover:cursor-pointer font-bold text-sm shadow-lg"
                style={{
                  backgroundColor: `var(--color-trainee)`,
                  color: colors.navy
                }}>
                Most Trusted JEE Platform
              </div>
            </motion.div>

            <motion.h1
              className="text-4xl md:text-6xl font-extrabold tracking-tight mb-6"
              style={{ color: colors.white }}>
              <motion.span
                style={{ color: colors.gold }}
                animate={{
                  textShadow: [
                    '0 0 8px rgba(255,215,0,0.3)',
                    '0 0 16px rgba(255,215,0,0.6)',
                    '0 0 8px rgba(255,215,0,0.3)'
                  ]
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity
                }}>
                IIT
              </motion.span>{' '}
              Dreams Begin Here
            </motion.h1>

            <motion.p
              className="text-xl md:text-2xl max-w-2xl mx-auto mb-8"
              style={{ color: colors.white }}>
              Trusted by <span className="font-bold">50,000+</span> aspirants across India
            </motion.p>

            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <button
                className="font-bold py-3 px-8 rounded-lg hover:cursor-pointer text-lg transition-all shadow-lg"
                onClick={() => navigate('/auth')}
                style={{
                  backgroundColor: colors.gold,
                  color: colors.navy
                }}>
                Begin Your Journey
              </button>
            </motion.div>
          </motion.div>

          <motion.div
            animate={{
              y: [0, -15, 0]
            }}
            transition={{
              duration: 3,
              repeat: Infinity,
              ease: 'easeInOut'
            }}
            className="absolute bottom-10">
            <svg
              className="w-8 h-8"
              style={{ color: colors.white }}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M19 14l-7 7m0 0l-7-7m7 7V3"
              />
            </svg>
          </motion.div>
        </motion.div>
      </section>

      {/* Features Section with Indian Patterns */}
      <section className="py-20 relative">
        <div className="absolute inset-0 overflow-hidden">
          <motion.div
            className="absolute inset-0 opacity-5"
            animate={{
              backgroundPosition: ['0% 0%', '100% 100%']
            }}
            transition={{
              duration: 30,
              repeat: Infinity,
              ease: 'linear'
            }}
            style={{
              backgroundImage: 'radial-gradient(circle, #000 1px, transparent 1px)',
              backgroundSize: '40px 40px'
            }}
          />
        </div>

        <div className="max-w-7xl mx-auto px-4 relative z-10">
          <motion.h2
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-3xl md:text-4xl font-bold text-center mb-16"
            style={{ color: colors.navy }}>
            Our <span style={{ color: `var(--color-trainee)` }}>Unique</span> Approach
          </motion.h2>

          <div className="grid md:grid-cols-3 gap-10">
            {[
              {
                icon: '✨',
                title: 'Adaptive AI Learning',
                desc: 'Smart algorithms that adjust to your learning pace'
              },
              {
                icon: '🎓',
                title: 'IIT Mentor Network',
                desc: 'Guidance from top rankers across all IITs'
              },
              {
                icon: '📚',
                title: 'Performance Analytics',
                desc: 'Detailed insights to track your progress'
              }
            ].map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{
                  y: -10,
                  boxShadow: `0 10px 25px -10px ${colors.navy}`
                }}
                className="bg-white hover:cursor-pointer p-6 rounded-xl shadow-md border-t-4"
                style={{ borderTopColor: `var(--color-counselor)` }}>
                <motion.div
                  className="text-5xl mb-4"
                  animate={{
                    scale: [1, 1.1, 1],
                    rotate: [0, 10, -10, 0]
                  }}
                  transition={{
                    duration: 5,
                    repeat: Infinity
                  }}>
                  {feature.icon}
                </motion.div>
                <h3 className="text-xl font-bold mb-3" style={{ color: colors.navy }}>
                  {feature.title}
                </h3>
                <p className="text-gray-600">{feature.desc}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Results Showcase with Indian Flag Colors */}
      <section className="py-20 relative overflow-hidden">
        <motion.div
          initial={{ scale: 1.2, opacity: 0 }}
          whileInView={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="absolute inset-0"
          style={{
            background: `var(--color-teacher)`,
            opacity: 0.1
          }}
        />

        <div className="max-w-7xl mx-auto px-4 relative z-10">
          <motion.h2
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-3xl md:text-4xl font-bold text-center mb-16"
            style={{ color: 'white' }}>
            Transforming <span style={{ color: colors.saffron }}>Education</span> Across India
          </motion.h2>

          <div className="grid hover:cursor-pointer grid-cols-2 md:grid-cols-4 gap-6">
            {[
              { value: '100+', label: 'IIT Top Rankers' },
              { value: '10K+', label: 'Advanced Qualifiers' },
              { value: '50K+', label: 'Mains 99+ Percentile' },
              { value: '1L+', label: 'Students Empowered' }
            ].map((stat, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{
                  scale: 1.05,
                  backgroundColor: colors.white,
                  color: colors.navy
                }}
                className="text-center p-6 rounded-xl shadow-md backdrop-blur-sm border border-white/20"
                style={{ backgroundColor: colors.navy, color: colors.white }}>
                <div className="text-4xl md:text-5xl font-bold mb-2" style={{ color: colors.gold }}>
                  {stat.value}
                </div>
                <div>{stat.label}</div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Final CTA with Peacock Motif */}
      <section className="py-2 relative overflow-hidden">
        <div className="max-w-6xl mx-auto px-4 py-16 md:py-24 relative z-10">
          <motion.div
            initial={{ y: 50, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true, margin: '-100px' }}
            className="grid md:grid-cols-2 gap-12 items-center">
            {/* Left Column - Content */}
            <div>
              <motion.div
                initial={{ opacity: 0 }}
                whileInView={{ opacity: 1 }}
                transition={{ delay: 0.2 }}
                viewport={{ once: true }}
                className="flex flex-col space-y-8">
                <div>
                  <motion.span
                    className="inline-block px-4 py-1 rounded-full text-sm font-semibold mb-4"
                    style={{
                      backgroundColor: `var(--color-counselor)`,
                      color: 'white'
                    }}
                    whileHover={{ scale: 1.05 }}>
                    🚀 Most Comprehensive JEE Program
                  </motion.span>

                  <motion.h2
                    className="text-4xl md:text-5xl font-bold mb-6 leading-tight"
                    style={{ color: 'var(--color-teacher' }}>
                    Your Complete{' '}
                    <span style={{ color: 'var(--color-trainee)' }}>IIT Preparation</span> Ecosystem
                  </motion.h2>

                  <motion.p className="text-xl  text-gray-600">
                    Join 50,000+ aspirants in India's most effective learning platform with:
                  </motion.p>
                </div>

                {/* Features List */}
                <motion.ul className="space-y-2">
                  {[
                    '✅ 100+ IITian Mentors',
                    '✅ 10,000+ Practice Problems',
                    '✅ AI-Powered Performance Analysis',
                    '✅ Daily Live Doubt Solving',
                    '✅ 200+ Hours of Video Lectures',
                    '✅ Personalized Study Plans'
                  ].map((item, index) => (
                    <motion.li
                      key={index}
                      className="flex items-start text-lg"
                      initial={{ x: -20, opacity: 0 }}
                      whileInView={{ x: 0, opacity: 1 }}
                      transition={{ delay: 0.1 * index }}
                      viewport={{ once: true }}>
                      <span className="mr-3" style={{ color: `var(--color-trainee)` }}>
                        ✓
                      </span>
                      <span className="text-gray-700">{item}</span>
                    </motion.li>
                  ))}
                </motion.ul>

                {/* CTA Section */}
              </motion.div>
            </div>

            {/* Right Column - Visual Content */}
            <motion.div
              className="relative"
              initial={{ opacity: 0, scale: 0.9 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.4 }}
              viewport={{ once: true }}>
              {/* Mockup/Illustration */}
              <div
                className="relative bg-white p-2 rounded-2xl shadow-2xl"
                style={{ border: `1px solid ${colors.saffron}20` }}>
                <div className="aspect-video bg-gradient-to-br from-blue-50 to-amber-50 rounded-xl overflow-hidden">
                  {/* You would replace this with your actual image/illustration */}
                  <div className="h-full flex items-center justify-center">
                    <div className="text-center p-8">
                      <div className="text-4xl mb-4" style={{ color: colors.saffron }}>
                        📚
                      </div>
                      <h3 className="text-xl font-bold mb-2" style={{ color: colors.navy }}>
                        Interactive Learning
                      </h3>
                      <p className="text-gray-600">Experience our platform</p>
                    </div>
                  </div>
                </div>

                <motion.div
                  className="absolute -bottom-6 -right-6 bg-white p-3 rounded-lg shadow-md"
                  style={{ border: `1px solid ${colors.saffron}20` }}
                  animate={{
                    y: [0, 10, 0],
                    rotate: [0, -5, 5, 0]
                  }}
                  transition={{
                    duration: 8,
                    repeat: Infinity,
                    ease: 'easeInOut',
                    delay: 1
                  }}>
                  <div className="text-2xl">🏆</div>
                </motion.div>
              </div>

              {/* Stats Badges */}
              <div className="absolute -bottom-15 left-0 right-0 flex justify-center gap-4">
                <motion.div
                  className="bg-[var(--color-teacher)] py-2 px-4 rounded-full shadow-md flex items-center"
                  style={{ border: `1px solid ${colors.saffron}20` }}
                  initial={{ y: 20, opacity: 0 }}
                  whileInView={{ y: 0, opacity: 1 }}
                  transition={{ delay: 0.6 }}
                  viewport={{ once: true }}>
                  <div
                    className="w-3 h-3 rounded-full mr-2"
                    style={{ backgroundColor: `var(--color-counselor)` }}></div>
                  <span className="font-medium text-white">95% Satisfaction</span>
                </motion.div>

                <motion.div
                  className="bg-[var(--color-teacher)] py-2 px-4 rounded-full shadow-md flex items-center"
                  style={{ border: `1px solid ${colors.saffron}20` }}
                  initial={{ y: 20, opacity: 0 }}
                  whileInView={{ y: 0, opacity: 1 }}
                  transition={{ delay: 0.8 }}
                  viewport={{ once: true }}>
                  <div
                    className="w-3 h-3 rounded-full mr-2"
                    style={{ backgroundColor: `var(--color-counselor)` }}></div>
                  <span className="font-medium text-white">3000+ Selections</span>
                </motion.div>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default JeePrepHome;
