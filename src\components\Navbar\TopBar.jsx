import React, { useState } from 'react';

import { Link, useNavigate } from 'react-router';
import { useDispatch } from 'react-redux';
import { clearAuthData, useUserLogoutServiceMutation } from '../../pages/auth/auth.slice';
import { clearMenuData } from './navbar.slice';
import { LogOut, Menu, X, Smile } from 'lucide-react';
import Toastify from '../PopUp/Toastify';
import { motion, AnimatePresence } from 'framer-motion';
import Logo from '../../assets/sasthra_logo.png';
const TopBar = ({ onToggleSidebar }) => {
  const roleColorMap = {
    director: 'bg-director',
    student: 'bg-student',
    center_counselor: 'bg-counselor',
    kota_teacher: 'bg-teacher',
    faculty: 'bg-trainee',
    parent: 'bg-parents'
  };
  const [res, setRes] = useState(null);
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [userLogout, setUserLogout] = useUserLogoutServiceMutation();
  const [isConfirmOpen, setIsConfirmOpen] = useState(false);

  const bgClass = roleColorMap[sessionStorage.role] || 'bg-white';

  const dropdownVariants = {
    hidden: { opacity: 0, y: -10 },
    visible: { opacity: 1, y: 0 },
    exit: { opacity: 0, y: -10 }
  };

  const handleLogout = async () => {
    try {
      // Get user_id from sessionStorage
      const userId = sessionStorage.getItem('userId');
      if (!userId) {
        console.warn('No userId found in sessionStorage');
        // Proceed with client-side cleanup if no userId
        sessionStorage.clear();
        dispatch(clearAuthData());
        dispatch(clearMenuData());
        navigate('/auth', { replace: true });
        return;
      }
      const response = await userLogout({ user_id: userId }).unwrap();
      setRes(response);

      // Clear sessionStorage and dispatch actions
      sessionStorage.clear();
      dispatch(clearAuthData());
      dispatch(clearMenuData());
      console.log('logout Successfull');
      navigate('/auth', { replace: true });
    } catch (error) {
      console.error('Logout failed:', error);
      // Proceed with client-side cleanup even if server request fails

      dispatch(clearAuthData());
      dispatch(clearMenuData());
      setRes(error);
      navigate('/auth', { replace: true });
    }
  };

  const userAvatar =
    'https://png.pngtree.com/png-vector/20240601/ourmid/pngtree-casual-man-flat-design-avatar-profile-picture-vector-png-image_12593008.png';

  return (
    <header
      className={`w-full ${bgClass} flex items-center justify-between px-4 h-20 shadow-md z-10`}>
      <div className="flex items-center gap-4">
        <Toastify res={res} resClear={() => setRes(null)} />
        <button
          onClick={onToggleSidebar}
          className="text-white p-2 rounded-full hover:bg-black/20 transition-colors">
          <Menu size={24} />
        </button>
        <Link to="/sasthra">
          {' '}
          {/* A more appropriate link like /dashboard */}
          <img alt="Sasthra Logo" src={Logo} className="h-16 w-full" />
        </Link>
      </div>

      <div className="relative group">
        <img
          src={userAvatar}
          alt="User Avatar"
          className="w-10 h-10 rounded-full border-2 border-white/50 cursor-pointer"
        />

        {/* Profile Dropdown */}
        <motion.div
          initial="hidden"
          animate="visible"
          exit="exit"
          variants={dropdownVariants}
          transition={{ duration: 0.2 }}
          className="absolute right-0 mt-2 w-56 bg-white rounded-xl shadow-xl py-1 hidden group-hover:block z-20 overflow-hidden">
          <Link
            to="/profile"
            className="block px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 transition-colors">
            View Profile
          </Link>

          <button
            onClick={() => setIsConfirmOpen(true)}
            className="w-full text-left flex items-center gap-2 px-4 py-3 hover:cursor-pointer text-sm text-red-600 hover:bg-red-50 transition-colors">
            <LogOut size={16} className="text-red-500" />
            <span>Logout</span>
          </button>
        </motion.div>

        {/* Logout Confirmation Modal */}
        <AnimatePresence>
          {isConfirmOpen && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 z-50 flex items-center justify-center bg-[var(--color-teacher)]/40 backdrop-blur-sm p-4">
              {/* Floating Card with Gentle Bounce */}
              <motion.div
                initial={{ y: 40, opacity: 0, scale: 0.96 }}
                animate={{ y: 0, opacity: 1, scale: 1 }}
                exit={{ y: 40, opacity: 0, scale: 0.96 }}
                transition={{
                  type: 'spring',
                  damping: 15,
                  stiffness: 300,
                  bounce: 0.25
                }}
                className="relative max-w-sm w-full bg-[var(--color-teacher)] rounded-2xl shadow-xl overflow-hidden border border-gray-100">
                {/* Floating Particles Background */}
                <div className="absolute inset-0 overflow-hidden opacity-5">
                  {[...Array(12)].map((_, i) => (
                    <motion.div
                      key={i}
                      className="absolute rounded-full bg-gray-400"
                      initial={{
                        x: Math.random() * 100,
                        y: Math.random() * 100,
                        width: Math.random() * 8 + 2,
                        height: Math.random() * 8 + 2
                      }}
                      animate={{
                        y: [null, Math.random() * 20 - 10]
                      }}
                      transition={{
                        duration: Math.random() * 4 + 3,
                        repeat: Infinity,
                        repeatType: 'reverse',
                        ease: 'easeInOut'
                      }}
                    />
                  ))}
                </div>

                {/* Content */}
                <div className="relative z-10 p-8">
                  {/* Animated Header */}
                  <motion.div
                    className="flex justify-between items-center mb-8"
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.1 }}>
                    <motion.h3
                      className="text-2xl font-semibold  text-[var(--color-counselor)]"
                      animate={{
                        x: [0, 2, -2, 0]
                      }}
                      transition={{
                        duration: 6,
                        repeat: Infinity,
                        ease: 'easeInOut'
                      }}>
                      Ready to leave?
                    </motion.h3>
                    <motion.button
                      whileHover={{ rotate: 90, backgroundColor: 'rgba(0,0,0,0.05)' }}
                      whileTap={{ scale: 0.9 }}
                      onClick={() => setIsConfirmOpen(false)}
                      className="p-1 rounded-full text-white hover:text-white hover:cursor-pointer transition-all">
                      <X size={20} />
                    </motion.button>
                  </motion.div>

                  {/* Animated User Avatar */}
                  <motion.div
                    className="flex flex-col items-center mb-8"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.2 }}>
                    <div className="relative w-70 h-30 mb-4">
                      <motion.div
                        className="absolute inset-0  border-gray-200"
                        animate={{
                          scale: [1, 1.05, 1],
                          opacity: [0.8, 1, 0.8]
                        }}
                        transition={{
                          duration: 3,
                          repeat: Infinity,
                          ease: 'easeInOut'
                        }}
                      />
                      <img
                        src={Logo}
                        alt="User"
                        className="w-full h-full rounded-full object-cover "
                      />
                    </div>
                    <p className="text-white text-center">
                      Do you want to Logout from <br />
                      <span className="font-medium text-[var(--color-counselor)]">
                        Sasthra Dashboard
                      </span>
                    </p>
                  </motion.div>

                  {/* Creative Action Buttons */}
                  <div className="flex flex-col gap-3">
                    <motion.button
                      whileHover={{
                        scale: 1.02,
                        boxShadow: '0 4px 12px rgba(0,0,0,0.08)',
                        backgroundColor: '#f3f4f6'
                      }}
                      whileTap={{ scale: 0.98 }}
                      onClick={() => setIsConfirmOpen(false)}
                      className="py-3 px-6 rounded-xl bg-gray-100 hover:cursor-pointer text-gray-700 font-medium transition-all relative overflow-hidden"
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.3 }}>
                      <motion.span
                        className="absolute inset-0 bg-gradient-to-r from-transparent via-white/50 to-transparent"
                        initial={{ x: '-100%' }}
                        animate={{ x: '100%' }}
                        transition={{
                          duration: 2,
                          repeat: Infinity,
                          delay: 0.5
                        }}
                      />
                      <span className="relative z-10 flex items-center justify-center gap-2">
                        <span className="text-[var(--color-teacher)]">Continue Working</span>

                        <motion.div
                          animate={{ rotate: [0, 10, -10, 0] }}
                          transition={{
                            duration: 3,
                            repeat: Infinity,
                            repeatType: 'mirror'
                          }}>
                          <Smile size={18} className="text-[var(--color-teacher)]" />
                        </motion.div>
                      </span>
                    </motion.button>

                    <motion.button
                      whileHover={{
                        scale: 1.02,
                        boxShadow: '0 4px 16px rgba(239,68,68,0.2)'
                      }}
                      whileTap={{ scale: 0.98 }}
                      onClick={handleLogout}
                      className="py-3 px-6 rounded-xl bg-[var(--color-counselor)] hover:cursor-pointer text-white font-medium transition-all relative overflow-hidden group"
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.4 }}>
                      <motion.span
                        className="absolute inset-0 bg-gradient-to-r from-transparent via-white/50 to-transparent"
                        initial={{ x: '-100%' }}
                        animate={{ x: '100%' }}
                        transition={{
                          duration: 2,
                          repeat: Infinity
                        }}
                      />
                      <span className="relative z-10 flex items-center justify-center gap-2">
                        <motion.span
                          animate={{ x: [0, 2, -2, 0] }}
                          transition={{
                            duration: 2,
                            repeat: Infinity
                          }}>
                          Sign Out
                        </motion.span>
                        <motion.div
                          animate={{
                            x: [0, 4, 0],
                            rotate: [0, 360]
                          }}
                          transition={{
                            duration: 1.5,
                            repeat: Infinity,
                            repeatType: 'reverse'
                          }}>
                          <LogOut size={18} />
                        </motion.div>
                      </span>
                    </motion.button>
                  </div>
                </div>

                {/* Decorative Floating Elements */}
                <motion.div
                  className="absolute top-4 right-4 w-3 h-3 rounded-full bg-red-400"
                  animate={{
                    y: [0, -8, 0],
                    opacity: [0.6, 1, 0.6]
                  }}
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    ease: 'easeInOut'
                  }}
                />
                <motion.div
                  className="absolute bottom-6 left-6 w-2 h-2 rounded-full bg-blue-400"
                  animate={{
                    y: [0, -6, 0],
                    opacity: [0.4, 0.8, 0.4]
                  }}
                  transition={{
                    duration: 4,
                    repeat: Infinity,
                    ease: 'easeInOut',
                    delay: 0.5
                  }}
                />
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </header>
  );
};

export default TopBar;
