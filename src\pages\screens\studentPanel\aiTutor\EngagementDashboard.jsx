import React, { useEffect, useState } from 'react';
import { X, BarChart3, Users, Activity, Trophy } from 'lucide-react';

const FLASK_API_URL = 'https://sasthra.in';

const EngagementDashboard = ({ quizId, onClose }) => {
  const [dashboardData, setDashboardData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);
        const response = await fetch(`${FLASK_API_URL}/content/engagement_dashboard?quiz_id=${quizId}`);
        const data = await response.json();
        
        if (!response.ok) {
          throw new Error(data.error || `Server error ${response.status}`);
        }
        
        setDashboardData(data);
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    if (quizId) {
      fetchDashboardData();
    }
  }, [quizId]);

  if (loading) {
    return (
      <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm rounded-xl p-8 border border-white/10 max-w-7xl mx-auto">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold text-white flex items-center gap-2">
            <BarChart3 size={24} />
            Engagement Dashboard
          </h2>
          <button onClick={onClose} className="text-white hover:text-blue-400">
            <X size={24} />
          </button>
        </div>
        <div className="flex justify-center items-center h-64">
          <div className="text-white text-lg">Loading dashboard data...</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm rounded-xl p-8 border border-white/10 max-w-7xl mx-auto">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold text-white flex items-center gap-2">
            <BarChart3 size={24} />
            Engagement Dashboard
          </h2>
          <button onClick={onClose} className="text-white hover:text-blue-400">
            <X size={24} />
          </button>
        </div>
        <div className="text-center text-red-400 p-4">
          <p>Error loading dashboard: {error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm rounded-xl p-8 border border-white/10 max-w-7xl mx-auto">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-white flex items-center gap-2">
          <BarChart3 size={24} />
          Engagement Dashboard
        </h2>
        <button onClick={onClose} className="text-white hover:text-blue-400">
          <X size={24} />
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        <div className="bg-gray-800/50 rounded-lg p-6 border border-gray-700">
          <div className="flex items-center gap-3 mb-4">
            <Trophy className="text-yellow-400" size={24} />
            <h3 className="text-lg font-semibold text-white">Quiz Overview</h3>
          </div>
          <div className="space-y-2 text-gray-300">
            <div>Quiz ID: <span className="text-white">{quizId}</span></div>
            <div>Status: <span className="text-green-400">Completed</span></div>
          </div>
        </div>

        <div className="bg-gray-800/50 rounded-lg p-6 border border-gray-700">
          <div className="flex items-center gap-3 mb-4">
            <Users className="text-blue-400" size={24} />
            <h3 className="text-lg font-semibold text-white">Participation</h3>
          </div>
          <div className="space-y-2 text-gray-300">
            <div>Total Participants: <span className="text-white">-</span></div>
            <div>Active Responses: <span className="text-white">-</span></div>
          </div>
        </div>

        <div className="bg-gray-800/50 rounded-lg p-6 border border-gray-700">
          <div className="flex items-center gap-3 mb-4">
            <Activity className="text-green-400" size={24} />
            <h3 className="text-lg font-semibold text-white">Activity</h3>
          </div>
          <div className="space-y-2 text-gray-300">
            <div>Hand Raises: <span className="text-white">-</span></div>
            <div>Avg Response Time: <span className="text-white">-</span></div>
          </div>
        </div>
      </div>

      <div className="bg-gray-800/50 rounded-lg p-6 border border-gray-700">
        <h3 className="text-lg font-semibold text-white mb-4">Raw Dashboard Data</h3>
        <pre className="bg-gray-900 p-4 rounded-lg text-gray-200 overflow-auto max-h-96 text-sm">
          {JSON.stringify(dashboardData, null, 2)}
        </pre>
      </div>
    </div>
  );
};

export default EngagementDashboard;