import { createSlice } from '@reduxjs/toolkit';
import { studentDashboardApi } from '../../../../redux/api/api';

const initialState = {
  scoreOverview: null,
  subjectWiseScore: null,
  attemptBreakdown: null,
  accuracyBySubject: null,
  timeAccuracyTrends: null,
  timeSpentDistribution: null,
  timeJourney: null,
  qualityOfTime: null,
  learningEfficiency: null,
  confusedAttempts: null,
  subjectSwitchTimeline: null,
  questionAttemptStatusTimeline: null,
  errorTopics: null,
  aiTutorFeedback: null,
  allQuestionsLog: null
};

export const studentDashboardApiSlice = studentDashboardApi.injectEndpoints({
  endpoints: (builder) => ({
    getScoreOverviewService: builder.query({
      query: (query) => {
        return `/api/analytics/score-overview/${query.userId}`;
      },
      transformResponse: (res) => {
        return res;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['StudentDashboard']
    }),
    getSubjectWiseScoreService: builder.query({
      query: (query) => {
        return `/api/analytics/subject-wise-score/${query.userId}`;
      },
      transformResponse: (res) => {
        return res;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['StudentDashboard']
    }),
    getAttemptBreakdownService: builder.query({
      query: (query) => {
        return `/api/analytics/attempt-breakdown/${query.userId}`;
      },
      transformResponse: (res) => {
        return res;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['StudentDashboard']
    }),
    getAccuracyBySubjectService: builder.query({
      query: (query) => {
        return `/api/analytics/accuracy-by-subject/${query.userId}`;
      },
      transformResponse: (res) => {
        return res;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['StudentDashboard']
    }),
    getTimeAccuracyTrendsService: builder.query({
      query: (query) => {
        return `/api/analytics/time-vs-accuracy-trends/${query.userId}`;
      },
      transformResponse: (res) => {
        return res;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['StudentDashboard']
    }),
    getTimeSpentDistributionService: builder.query({
      query: (query) => {
        return `/api/analytics/time-spent-distribution/${query.userId}`;
      },
      transformResponse: (res) => {
        return res;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['StudentDashboard']
    }),
    getTimeJourneyService: builder.query({
      query: (query) => {
        return `/api/analytics/time-journey/${query.userId}`;
      },
      transformResponse: (res) => {
        return res;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['StudentDashboard']
    }),
    getQualityOfTimeService: builder.query({
      query: (query) => {
        return `/api/analytics/quality-of-time/${query.userId}`;
      },
      transformResponse: (res) => {
        return res;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['StudentDashboard']
    }),
    getLearningEfficiencyService: builder.query({
      query: (query) => {
        return `/api/analytics/learning-efficiency/${query.userId}`;
      },
      transformResponse: (res) => {
        return res;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['StudentDashboard']
    }),
    getConfusedAttemptsService: builder.query({
      query: (query) => {
        return `/api/analytics/confused-attempts/${query.userId}`;
      },
      transformResponse: (res) => {
        return res;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['StudentDashboard']
    }),
    getSubjectSwitchTimelineService: builder.query({
      query: (query) => {
        return `/api/analytics/subject-switch-timeline/${query.userId}`;
      },
      transformResponse: (res) => {
        return res;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['StudentDashboard']
    }),
    getQuestionAttemptStatusTimelineService: builder.query({
      query: (query) => {
        return `/api/analytics/question-attempt-status-timeline/${query.userId}`;
      },
      transformResponse: (res) => {
        return res;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['StudentDashboard']
    }),
    getErrorTopicsService: builder.query({
      query: (query) => {
        return `/api/analytics/error-topics/${query.userId}`;
      },
      transformResponse: (res) => {
        return res;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['StudentDashboard']
    }),
    getAiTutorFeedbackService: builder.query({
      query: (query) => {
        return `/api/analytics/ai-tutor-feedback/${query.userId}`;
      },
      transformResponse: (res) => {
        return res;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['StudentDashboard']
    }),
    getAllQuestionsLogService: builder.query({
      query: (query) => {
        return `/api/analytics/all-questions-log/${query.userId}`;
      },
      transformResponse: (res) => {
        return res;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      })
    })
  })
});

const studentDashboardSlice = createSlice({
  name: 'studentDashboard',
  initialState,
  reducers: {
    setScoreOverview(state, action) {
      state.scoreOverview = action.payload;
    },
    clearScoreOverview(state) {
      state.scoreOverview = null;
    },
    setSubjectWiseScore(state, action) {
      state.subjectWiseScore = action.payload;
    },
    clearSubjectWiseScore(state) {
      state.subjectWiseScore = null;
    },
    setAttemptBreakdown(state, action) {
      state.attemptBreakdown = action.payload;
    },
    clearAttemptBreakdown(state) {
      state.attemptBreakdown = null;
    },
    setAccuracyBySubject(state, action) {
      state.accuracyBySubject = action.payload;
    },
    clearAccuracyBySubject(state) {
      state.accuracyBySubject = null;
    },
    setTimeAccuracyTrends(state, action) {
      state.timeAccuracyTrends = action.payload;
    },
    clearTimeAccuracyTrends(state) {
      state.timeAccuracyTrends = null;
    },
    setTimeSpentDistribution(state, action) {
      state.timeSpentDistribution = action.payload;
    },
    clearTimeSpentDistribution(state) {
      state.timeSpentDistribution = null;
    },
    setTimeJourney(state, action) {
      state.timeJourney = action.payload;
    },
    clearTimeJourney(state) {
      state.timeJourney = null;
    },
    setQualityOfTime(state, action) {
      state.qualityOfTime = action.payload;
    },
    clearQualityOfTime(state) {
      state.qualityOfTime = null;
    },
    setLearningEfficiency(state, action) {
      state.learningEfficiency = action.payload;
    },
    clearLearningEfficiency(state) {
      state.learningEfficiency = null;
    },
    setConfusedAttempts(state, action) {
      state.confusedAttempts = action.payload;
    },
    clearConfusedAttempts(state) {
      state.confusedAttempts = null;
    },
    setSubjectSwitchTimeline(state, action) {
      state.subjectSwitchTimeline = action.payload;
    },
    clearSubjectSwitchTimeline(state) {
      state.subjectSwitchTimeline = null;
    },
    setQuestionAttemptStatusTimeline(state, action) {
      state.questionAttemptStatusTimeline = action.payload;
    },
    clearQuestionAttemptStatusTimeline(state) {
      state.questionAttemptStatusTimeline = null;
    },
    setErrorTopics(state, action) {
      state.errorTopics = action.payload;
    },
    clearErrorTopics(state) {
      state.errorTopics = null;
    },
    setAiTutorFeedback(state, action) {
      state.aiTutorFeedback = action.payload;
    },
    clearAiTutorFeedback(state) {
      state.aiTutorFeedback = null;
    },
    setAllQuestionsLog(state, action) {
      state.allQuestionsLog = action.payload;
    },
    clearAllQuestionsLog(state) {
      state.allQuestionsLog = null;
    }
  }
});

export const {
  useLazyGetScoreOverviewServiceQuery,
  useLazyGetSubjectWiseScoreServiceQuery,
  useLazyGetAttemptBreakdownServiceQuery,
  useLazyGetAccuracyBySubjectServiceQuery,
  useLazyGetTimeAccuracyTrendsServiceQuery,
  useLazyGetTimeSpentDistributionServiceQuery,
  useLazyGetTimeJourneyServiceQuery,
  useLazyGetQualityOfTimeServiceQuery,
  useLazyGetLearningEfficiencyServiceQuery,
  useLazyGetConfusedAttemptsServiceQuery,
  useLazyGetSubjectSwitchTimelineServiceQuery,
  useLazyGetQuestionAttemptStatusTimelineServiceQuery,
  useLazyGetErrorTopicsServiceQuery,
  useLazyGetAiTutorFeedbackServiceQuery,
  useLazyGetAllQuestionsLogServiceQuery
} = studentDashboardApiSlice;
export const {
  setScoreOverview,
  clearScoreOverview,
  setSubjectWiseScore,
  clearSubjectWiseScore,
  setAttemptBreakdown,
  clearAttemptBreakdown,
  setAccuracyBySubject,
  clearAccuracyBySubject,
  setTimeAccuracyTrends,
  clearTimeAccuracyTrends,
  setTimeSpentDistribution,
  clearTimeSpentDistribution,
  setTimeJourney,
  clearTimeJourney,
  setQualityOfTime,
  clearQualityOfTime,
  setLearningEfficiency,
  clearLearningEfficiency,
  setConfusedAttempts,
  clearConfusedAttempts,
  setSubjectSwitchTimeline,
  clearSubjectSwitchTimeline,
  setQuestionAttemptStatusTimeline,
  clearQuestionAttemptStatusTimeline,
  setErrorTopics,
  clearErrorTopics,
  setAiTutorFeedback,
  clearAiTutorFeedback,
  setAllQuestionsLog,
  clearAllQuestionsLog
} = studentDashboardSlice.actions;
export const selectScoreOverview = (state) => state.studentDashboard.scoreOverview;
export default studentDashboardSlice.reducer;
