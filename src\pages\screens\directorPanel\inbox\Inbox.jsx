'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  useDirectorInboxServiceQuery,
  useDirectorApproveStudentServiceMutation,
  useDirectorApproveFacultyServiceMutation
} from '../addCenter/addCenter.Slice';
import Toastify from '../../../../components/PopUp/Toastify';
import {
  FiInbox,
  FiUser,
  FiUserCheck,
  FiMail,
  FiPhone,
  FiCalendar,
  FiBook,
  FiUsers,
  FiClock,
  FiMapPin,
  FiCheck,
  FiAlertCircle
} from 'react-icons/fi';

const Inbox = () => {
  const {
    data: inboxRequests = [],
    isLoading: inboxLoading,
    isError: inboxError,
    error: inboxErrorData
  } = useDirectorInboxServiceQuery();
  const [approveStudent] = useDirectorApproveStudentServiceMutation();
  const [approveFaculty] = useDirectorApproveFacultyServiceMutation();
  const [res, setRes] = useState(null);
  const [loadingRequestId, setLoadingRequestId] = useState(null);

  const handleApproveStudent = async (requestId) => {
    setLoadingRequestId(requestId);
    try {
      const response = await approveStudent(requestId).unwrap();
      setRes({
        status: 'success',
        message: `Student approved! Credentials: Student - ${response.credentials.student_credentials?.username}:${response.credentials.student_credentials?.password}, Parent - ${response.credentials.parent_credentials?.username}:${response.credentials.parent_credentials?.password}`
      });
    } catch (error) {
      setRes({
        status: 'error',
        message: error.message || 'Failed to approve student'
      });
    } finally {
      setLoadingRequestId(null);
    }
  };

  const handleApproveFaculty = async (requestId) => {
    setLoadingRequestId(requestId);
    try {
      const response = await approveFaculty(requestId).unwrap();
      setRes({
        status: 'success',
        message: `Faculty approved! Credentials: ${response.credentials.faculty_credentials?.username}:${response.credentials.faculty_credentials?.password}`
      });
    } catch (error) {
      setRes({
        status: 'error',
        message: error.message || 'Failed to approve faculty'
      });
    } finally {
      setLoadingRequestId(null);
    }
  };

  const containerVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.4, ease: 'easeOut' }
    }
  };

  const cardVariants = {
    hidden: { opacity: 0, scale: 0.95 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: { duration: 0.3 }
    },
    hover: {
      scale: 1.02,
      boxShadow: '0px 10px 30px rgba(0, 0, 0, 0.1)',
      transition: { duration: 0.2 }
    }
  };

  const buttonVariants = {
    hover: {
      scale: 1.05,
      boxShadow: '0px 8px 20px rgba(0, 0, 0, 0.15)',
      transition: { duration: 0.2 }
    },
    tap: { scale: 0.95 }
  };

  if (inboxLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100 p-4 md:p-8 flex items-center justify-center">
        <motion.div
          className="text-center"
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5 }}>
          <motion.div
            className="inline-block w-12 h-12 border-4 border-gray-300 border-t-[var(--color-director)] rounded-full mb-4"
            animate={{ rotate: 360 }}
            transition={{ duration: 1, repeat: Number.POSITIVE_INFINITY, ease: 'linear' }}
          />
          <p className="text-gray-600 text-lg">Loading approval requests...</p>
        </motion.div>
      </div>
    );
  }

  if (inboxError) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100 p-4 md:p-8 flex items-center justify-center">
        <motion.div
          className="text-center bg-white rounded-3xl p-8 shadow-2xl"
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5 }}>
          <FiAlertCircle className="mx-auto text-6xl text-red-500 mb-4" />
          <h3 className="text-xl font-bold text-gray-900 mb-2">Error Loading Requests</h3>
          <p className="text-red-500">
            {inboxErrorData?.message || 'Failed to load inbox requests'}
          </p>
        </motion.div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100 p-4 md:p-8">
      <Toastify res={res} resClear={() => setRes(null)} />

      <motion.div
        className="max-w-6xl mx-auto"
        variants={containerVariants}
        initial="hidden"
        animate="visible">
        {/* Header Section */}
        <motion.div className="text-center mb-8" variants={itemVariants}>
          <motion.div
            className="inline-flex items-center  justify-center w-16 h-16 rounded-full mb-4"
            style={{
              background: `linear-gradient(135deg, var(--color-director), var(--color-director))`,
              boxShadow: '0px 10px 30px rgba(0, 0, 0, 0.1)'
            }}
            whileHover={{ scale: 1.1, rotate: 5 }}>
            <FiInbox className="text-2xl" style={{ color: 'white' }} />
          </motion.div>
          <motion.h1 className="text-4xl font-bold mb-2" style={{ color: 'var(--color-director)' }}>
            Approval Requests
          </motion.h1>
          <motion.p className="text-gray-600 text-lg">
            Review and approve student and faculty registration requests
          </motion.p>
          <motion.div className="mt-4 inline-flex items-center px-4 py-2 bg-white rounded-full shadow-lg">
            <FiClock className="mr-2 text-gray-500" />
            <span className="text-gray-700 font-medium">
              {inboxRequests.length} pending requests
            </span>
          </motion.div>
        </motion.div>

        {/* Requests Container */}
        <motion.div
          className="bg-white rounded-3xl shadow-2xl overflow-hidden"
          variants={itemVariants}
          style={{
            background: 'linear-gradient(145deg, #ffffff 0%, #f8fafc 100%)',
            border: '1px solid rgba(0, 0, 0, 0.05)'
          }}>
          {inboxRequests.length === 0 ? (
            <div className="p-12 text-center">
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5 }}>
                <FiInbox className="mx-auto text-6xl text-gray-300 mb-4" />
                <h3 className="text-xl font-bold text-gray-900 mb-2">No Pending Requests</h3>
                <p className="text-gray-600">All approval requests have been processed</p>
              </motion.div>
            </div>
          ) : (
            <div className="p-8">
              <motion.div className="space-y-6" variants={containerVariants}>
                <AnimatePresence>
                  {inboxRequests.map((request, index) => (
                    <motion.div
                      key={request.id}
                      className="bg-gradient-to-br from-white to-gray-50 rounded-2xl shadow-lg border border-gray-100 overflow-hidden"
                      variants={cardVariants}
                      initial="hidden"
                      animate="visible"
                      exit="hidden"
                      whileHover="hover"
                      transition={{ delay: index * 0.1 }}>
                      {/* Request Header */}
                      <div
                        className="px-6 py-4 text-white"
                        style={{
                          background: `linear-gradient(135deg, var(--color-director), var(--color-director)dd)`
                        }}>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <motion.div
                              className="w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center mr-4"
                              whileHover={{ scale: 1.1, rotate: 5 }}>
                              {request.request_type === 'student_registration' ? (
                                <FiUser className="text-xl" />
                              ) : (
                                <FiUserCheck className="text-xl" />
                              )}
                            </motion.div>
                            <div>
                              <h3 className="text-xl font-bold">
                                {request.request_type === 'student_registration'
                                  ? 'Student Registration'
                                  : 'Faculty Registration'}
                              </h3>
                              <p className="text-white text-opacity-90">
                                From: {request.center_name} ({request.center_code})
                              </p>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="flex items-center text-white text-opacity-90 text-sm">
                              <FiCalendar className="mr-1" />
                              {new Date(request.created_at).toLocaleDateString()}
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Request Content */}
                      <div className="p-6">
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                          {/* Personal Information */}
                          <div className="space-y-4">
                            <h4 className="text-lg font-semibold text-gray-900 flex items-center">
                              <FiUser className="mr-2" style={{ color: 'var(--color-director)' }} />
                              Personal Information
                            </h4>
                            <div className="space-y-3">
                              <div className="flex items-center">
                                <FiUser className="mr-3 text-gray-400" />
                                <div>
                                  <p className="text-sm text-gray-600">Full Name</p>
                                  <p className="font-medium text-gray-900">
                                    {request.request_data.first_name}{' '}
                                    {request.request_data.last_name}
                                  </p>
                                </div>
                              </div>
                              <div className="flex items-center">
                                <FiMail className="mr-3 text-gray-400" />
                                <div>
                                  <p className="text-sm text-gray-600">Email</p>
                                  <p className="font-medium text-gray-900">
                                    {request.request_data.student_email}
                                  </p>
                                </div>
                              </div>
                              <div className="flex items-center">
                                <FiPhone className="mr-3 text-gray-400" />
                                <div>
                                  <p className="text-sm text-gray-600">Phone</p>
                                  <p className="font-medium text-gray-900">
                                    {request.request_data.phone}
                                  </p>
                                </div>
                              </div>
                              <div className="flex items-center">
                                <FiCalendar className="mr-3 text-gray-400" />
                                <div>
                                  <p className="text-sm text-gray-600">Date of Birth</p>
                                  <p className="font-medium text-gray-900">
                                    {request.request_data.dob}
                                  </p>
                                </div>
                              </div>
                            </div>
                          </div>

                          {/* Additional Information */}
                          <div className="space-y-4">
                            <h4 className="text-lg font-semibold text-gray-900 flex items-center">
                              {request.request_type === 'student_registration' ? (
                                <>
                                  <FiBook
                                    className="mr-2"
                                    style={{ color: 'var(--color-director)' }}
                                  />
                                  Academic Information
                                </>
                              ) : (
                                <>
                                  <FiMapPin
                                    className="mr-2"
                                    style={{ color: 'var(--color-director)' }}
                                  />
                                  Center Information
                                </>
                              )}
                            </h4>
                            <div className="space-y-3">
                              {request.request_type === 'student_registration' && (
                                <>
                                  <div className="flex items-center">
                                    <FiBook className="mr-3 text-gray-400" />
                                    <div>
                                      <p className="text-sm text-gray-600">Course</p>
                                      <p className="font-medium text-gray-900">
                                        {request.request_data.course}
                                      </p>
                                    </div>
                                  </div>
                                  <div className="flex items-center">
                                    <FiUsers className="mr-3 text-gray-400" />
                                    <div>
                                      <p className="text-sm text-gray-600">Parent/Guardian</p>
                                      <p className="font-medium text-gray-900">
                                        {request.request_data.parent_first_name} (
                                        {request.request_data.parent_relationship})
                                      </p>
                                    </div>
                                  </div>
                                </>
                              )}
                              <div className="flex items-center">
                                <FiMapPin className="mr-3 text-gray-400" />
                                <div>
                                  <p className="text-sm text-gray-600">Center</p>
                                  <p className="font-medium text-gray-900">
                                    {request.center_name} ({request.center_code})
                                  </p>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Action Button */}
                        <div className="mt-6 pt-6 border-t border-gray-200">
                          <motion.button
                            onClick={() =>
                              request.request_type === 'student_registration'
                                ? handleApproveStudent(request.id)
                                : handleApproveFaculty(request.id)
                            }
                            disabled={loadingRequestId === request.id}
                            className="w-full md:w-auto  px-8 py-3 rounded-xl font-semibold text-white text-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-3"
                            style={{
                              background: `linear-gradient(135deg, var(--color-director), var(--color-director))`,
                              boxShadow: '0px 4px 15px rgba(0, 0, 0, 0.1)'
                            }}
                            variants={buttonVariants}
                            whileHover="hover"
                            whileTap="tap">
                            {loadingRequestId === request.id ? (
                              <>
                                <motion.div
                                  className="w-5 h-5 border-2 border-white border-t-transparent rounded-full"
                                  animate={{ rotate: 360 }}
                                  transition={{
                                    duration: 1,
                                    repeat: Number.POSITIVE_INFINITY,
                                    ease: 'linear'
                                  }}
                                />
                                <span>Processing...</span>
                              </>
                            ) : (
                              <>
                                <FiCheck size={20} />
                                <span>
                                  Approve{' '}
                                  {request.request_type === 'student_registration'
                                    ? 'Student'
                                    : 'Faculty'}
                                </span>
                              </>
                            )}
                          </motion.button>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </AnimatePresence>
              </motion.div>
            </div>
          )}
        </motion.div>
      </motion.div>
    </div>
  );
};

export default Inbox;
