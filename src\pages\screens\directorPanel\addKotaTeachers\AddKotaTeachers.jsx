"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import {
  useDirectorAddKotaTeacherServiceMutation,
  
} from "./addKotaTeacher.Slice"; // Adjust path if needed
import { useListCoursesServiceQuery } from "../courses/addCourses.Slice";
import { useListSubjectsQuery } from "../subjects/subjects.Slice";
import Toastify from "../../../../components/PopUp/Toastify";
import {
  FiEye,
  FiEyeOff,
  FiUser,
  FiMail,
  FiPhone,
  FiLock,
  FiBook,
  FiUserPlus,
} from "react-icons/fi";

const AddKotaTeachers = () => {
  const [addKotaTeacher, { isLoading: loading }] = useDirectorAddKotaTeacherServiceMutation();
  const { data: courses = [] } = useListCoursesServiceQuery(); // Fetch courses dynamically
  const { data: subjects = [] } = useListSubjectsQuery(); // Fetch subjects dynamically
  const [res, setRes] = useState(null);
  const [showPassword, setShowPassword] = useState(false);
  const [kotaTeacherForm, setKotaTeacherForm] = useState({
    username: "",
    password: "",
    first_name: "",
    last_name: "",
    email: "",
    phone: "",
    course_id: "", // Changed from 'course' to 'course_id' to match typical API response
    subject_id: "", // Added for subject selection
  });
  const [errors, setErrors] = useState({
    username: "",
    password: "",
    first_name: "",
    last_name: "",
    email: "",
    phone: "",
    course_id: "",
    subject_id: "",
  });

  // Validation functions
  const validateUsername = (username) => {
    const usernameRegex = /^[a-zA-Z0-9_]{3,}$/;
    if (!username) return "Username is required";
    if (!usernameRegex.test(username))
      return "Username must be at least 3 characters long and contain only letters, numbers, or underscores";
    return "";
  };

  const validatePassword = (password) => {
    const passwordRegex =
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
    if (!password) return "Password is required";
    if (!passwordRegex.test(password))
      return "Password must be at least 8 characters long, with one uppercase, one lowercase, one number, and one special character";
    return "";
  };

  const validateFirstName = (first_name) => {
    const nameRegex = /^[a-zA-Z]{2,}$/;
    if (!first_name) return "First name is required";
    if (!nameRegex.test(first_name))
      return "First name must be at least 2 characters long and contain only letters";
    return "";
  };

  const validateLastName = (last_name) => {
    const nameRegex = /^[a-zA-Z]{2,}$/;
    if (!last_name) return "Last name is required";
    if (!nameRegex.test(last_name))
      return "Last name must be at least 2 characters long and contain only letters";
    return "";
  };

  const validateEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!email) return "Email is required";
    if (!emailRegex.test(email)) return "Please enter a valid email address";
    return "";
  };

  const validatePhone = (phone) => {
    if (!phone) return "Phone number is required";
    const digitsOnly = phone.replace(/\D/g, "");
    const phoneRegex = /^\+?[\d\s()-]*$/;
    if (!phoneRegex.test(phone))
      return "Phone number can only contain digits, spaces, hyphens, parentheses, or start with +";
    if (digitsOnly.length < 10 || digitsOnly.length > 12)
      return "Phone number must contain at least 10 digits";
    return "";
  };

  const validateCourseId = (course_id) => {
    if (!course_id) return "Course is required";
    return "";
  };

  const validateSubjectId = (subject_id) => {
    if (!subject_id) return "Subject is required";
    return "";
  };

  // Handle input change with validation
  const handleInputChange = (field, value) => {
    setKotaTeacherForm({ ...kotaTeacherForm, [field]: value });

    let error = "";
    switch (field) {
      case "username":
        error = validateUsername(value);
        break;
      case "password":
        error = validatePassword(value);
        break;
      case "first_name":
        error = validateFirstName(value);
        break;
      case "last_name":
        error = validateLastName(value);
        break;
      case "email":
        error = validateEmail(value);
        break;
      case "phone":
        error = validatePhone(value);
        break;
      case "course_id":
        error = validateCourseId(value);
        break;
      case "subject_id":
        error = validateSubjectId(value);
        break;
      default:
        break;
    }
    setErrors({ ...errors, [field]: error });
  };

  // Toggle password visibility
  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  // Validate all fields before submission
  const validateForm = () => {
    const newErrors = {
      username: validateUsername(kotaTeacherForm.username),
      password: validatePassword(kotaTeacherForm.password),
      first_name: validateFirstName(kotaTeacherForm.first_name),
      last_name: validateLastName(kotaTeacherForm.last_name),
      email: validateEmail(kotaTeacherForm.email),
      phone: validatePhone(kotaTeacherForm.phone),
      course_id: validateCourseId(kotaTeacherForm.course_id),
      subject_id: validateSubjectId(kotaTeacherForm.subject_id),
    };
    setErrors(newErrors);
    return Object.values(newErrors).every((error) => error === "");
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!validateForm()) {
      setRes({ status: "error", message: "Please fix the errors in the form." });
      return;
    }
    try {
      const response = await addKotaTeacher(kotaTeacherForm).unwrap();
      setRes({
        status: "success",
        message: response.message || "Kota teacher added successfully!",
      });
      setKotaTeacherForm({
        username: "",
        password: "",
        first_name: "",
        last_name: "",
        email: "",
        phone: "",
        course_id: "",
        subject_id: "",
      });
      setErrors({
        username: "",
        password: "",
        first_name: "",
        last_name: "",
        email: "",
        phone: "",
        course_id: "",
        subject_id: "",
      });
    } catch (error) {
      setRes({
        status: "error",
        message: error?.data?.message || "Failed to add Kota teacher. Please try again.",
      });
    }
  };

  const containerVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut",
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.4, ease: "easeOut" },
    },
  };

  const inputVariants = {
    hover: {
      scale: 1.02,
      boxShadow: "0px 8px 25px rgba(0, 0, 0, 0.1)",
      transition: { duration: 0.2 },
    },
    focus: {
      scale: 1.01,
      boxShadow: "0px 0px 0px 3px rgba(var(--color-director-rgb), 0.1)",
      transition: { duration: 0.2 },
    },
  };

  const buttonVariants = {
    hover: {
      scale: 1.02,
      boxShadow: "0px 10px 30px rgba(0, 0, 0, 0.2)",
      transition: { duration: 0.2 },
    },
    tap: { scale: 0.98 },
  };

  const iconVariants = {
    hover: { rotate: 5, scale: 1.1 },
    tap: { rotate: -5, scale: 0.9 },
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100 p-4 md:p-8">
      <Toastify res={res} resClear={() => setRes(null)} />

      <motion.div
        className="max-w-4xl mx-auto"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        {/* Header Section */}
        <motion.div className="text-center mb-8" variants={itemVariants}>
          <motion.div
            className="inline-flex items-center justify-center w-16 h-16 rounded-full mb-4"
            style={{
              background: `linear-gradient(135deg, var(--color-director), var(--color-director))`,
              boxShadow: "0px 10px 30px rgba(0, 0, 0, 0.1)",
            }}
            whileHover={{ scale: 1.1, rotate: 5 }}
            whileTap={{ scale: 0.9 }}
          >
            <FiUserPlus className="text-2xl" style={{ color: "white" }} />
          </motion.div>
          <motion.h1
            className="text-4xl font-bold mb-2"
            style={{ color: "var(--color-director)" }}
          >
            Add Kota Teacher
          </motion.h1>
          <motion.p className="text-gray-600 text-lg">
            Create a new Kota teacher with all the required information
          </motion.p>
        </motion.div>

        {/* Form Card */}
        <motion.div
          className="bg-white rounded-3xl shadow-2xl overflow-hidden"
          variants={itemVariants}
          style={{
            background: "linear-gradient(145deg, #ffffff 0%, #f8fafc 100%)",
            border: "1px solid rgba(0, 0, 0, 0.05)",
          }}
        >
          <div className="p-8 md:p-12">
            <form onSubmit={handleSubmit} className="space-y-8">
              {/* Form Grid */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Username */}
                <motion.div variants={itemVariants} className="space-y-2">
                  <label className="flex items-center text-sm font-semibold text-gray-700 mb-2">
                    <FiUser className="mr-2" style={{ color: "var(--color-director)" }} />
                    Username
                  </label>
                  <div className="relative">
                    <motion.input
                      type="text"
                      placeholder="Enter username"
                      value={kotaTeacherForm.username}
                      onChange={(e) => handleInputChange("username", e.target.value)}
                      className={`w-full border-2 ${
                        errors.username ? "border-red-400" : "border-gray-200"
                      } rounded-xl px-4 py-4 pl-12 focus:outline-none transition-all duration-300 bg-gray-50 focus:bg-white`}
                      style={{
                        focusBorderColor: "var(--color-director)",
                        fontSize: "16px",
                      }}
                      required
                      variants={inputVariants}
                      whileHover="hover"
                      whileFocus="focus"
                    />
                    <FiUser className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  </div>
                  {errors.username && (
                    <motion.p
                      className="text-red-500 text-sm flex items-center"
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                    >
                      <span className="mr-1">⚠</span> {errors.username}
                    </motion.p>
                  )}
                </motion.div>

                {/* First Name */}
                <motion.div variants={itemVariants} className="space-y-2">
                  <label className="flex items-center text-sm font-semibold text-gray-700 mb-2">
                    <FiUser className="mr-2" style={{ color: "var(--color-director)" }} />
                    First Name
                  </label>
                  <div className="relative">
                    <motion.input
                      type="text"
                      placeholder="Enter first name"
                      value={kotaTeacherForm.first_name}
                      onChange={(e) => handleInputChange("first_name", e.target.value)}
                      className={`w-full border-2 ${
                        errors.first_name ? "border-red-400" : "border-gray-200"
                      } rounded-xl px-4 py-4 pl-12 focus:outline-none transition-all duration-300 bg-gray-50 focus:bg-white`}
                      style={{
                        focusBorderColor: "var(--color-director)",
                        fontSize: "16px",
                      }}
                      required
                      variants={inputVariants}
                      whileHover="hover"
                      whileFocus="focus"
                    />
                    <FiUser className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  </div>
                  {errors.first_name && (
                    <motion.p
                      className="text-red-500 text-sm flex items-center"
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                    >
                      <span className="mr-1">⚠</span> {errors.first_name}
                    </motion.p>
                  )}
                </motion.div>

                {/* Last Name */}
                <motion.div variants={itemVariants} className="space-y-2">
                  <label className="flex items-center text-sm font-semibold text-gray-700 mb-2">
                    <FiUser className="mr-2" style={{ color: "var(--color-director)" }} />
                    Last Name
                  </label>
                  <div className="relative">
                    <motion.input
                      type="text"
                      placeholder="Enter last name"
                      value={kotaTeacherForm.last_name}
                      onChange={(e) => handleInputChange("last_name", e.target.value)}
                      className={`w-full border-2 ${
                        errors.last_name ? "border-red-400" : "border-gray-200"
                      } rounded-xl px-4 py-4 pl-12 focus:outline-none transition-all duration-300 bg-gray-50 focus:bg-white`}
                      style={{
                        focusBorderColor: "var(--color-director)",
                        fontSize: "16px",
                      }}
                      required
                      variants={inputVariants}
                      whileHover="hover"
                      whileFocus="focus"
                    />
                    <FiUser className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  </div>
                  {errors.last_name && (
                    <motion.p
                      className="text-red-500 text-sm flex items-center"
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                    >
                      <span className="mr-1">⚠</span> {errors.last_name}
                    </motion.p>
                  )}
                </motion.div>

                {/* Email */}
                <motion.div variants={itemVariants} className="space-y-2">
                  <label className="flex items-center text-sm font-semibold text-gray-700 mb-2">
                    <FiMail className="mr-2" style={{ color: "var(--color-director)" }} />
                    Email Address
                  </label>
                  <div className="relative">
                    <motion.input
                      type="email"
                      placeholder="Enter email address"
                      value={kotaTeacherForm.email}
                      onChange={(e) => handleInputChange("email", e.target.value)}
                      className={`w-full border-2 ${
                        errors.email ? "border-red-400" : "border-gray-200"
                      } rounded-xl px-4 py-4 pl-12 focus:outline-none transition-all duration-300 bg-gray-50 focus:bg-white`}
                      style={{
                        focusBorderColor: "var(--color-director)",
                        fontSize: "16px",
                      }}
                      required
                      variants={inputVariants}
                      whileHover="hover"
                      whileFocus="focus"
                    />
                    <FiMail className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  </div>
                  {errors.email && (
                    <motion.p
                      className="text-red-500 text-sm flex items-center"
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                    >
                      <span className="mr-1">⚠</span> {errors.email}
                    </motion.p>
                  )}
                </motion.div>

                {/* Phone */}
                <motion.div variants={itemVariants} className="space-y-2">
                  <label className="flex items-center text-sm font-semibold text-gray-700 mb-2">
                    <FiPhone className="mr-2" style={{ color: "var(--color-director)" }} />
                    Phone Number
                  </label>
                  <div className="relative">
                    <motion.input
                      type="tel"
                      placeholder="Enter phone number"
                      value={kotaTeacherForm.phone}
                      onChange={(e) => handleInputChange("phone", e.target.value)}
                      className={`w-full border-2 ${
                        errors.phone ? "border-red-400" : "border-gray-200"
                      } rounded-xl px-4 py-4 pl-12 focus:outline-none transition-all duration-300 bg-gray-50 focus:bg-white`}
                      style={{
                        focusBorderColor: "var(--color-director)",
                        fontSize: "16px",
                      }}
                      required
                      variants={inputVariants}
                      whileHover="hover"
                      whileFocus="focus"
                    />
                    <FiPhone className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  </div>
                  {errors.phone && (
                    <motion.p
                      className="text-red-500 text-sm flex items-center"
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                    >
                      <span className="mr-1">⚠</span> {errors.phone}
                    </motion.p>
                  )}
                </motion.div>

                {/* Course */}
                <motion.div variants={itemVariants} className="space-y-2">
                  <label className="flex items-center text-sm font-semibold text-gray-700 mb-2">
                    <FiBook className="mr-2" style={{ color: "var(--color-director)" }} />
                    Course
                  </label>
                  <div className="relative">
                    <motion.select
                      value={kotaTeacherForm.course_id}
                      onChange={(e) => handleInputChange("course_id", e.target.value)}
                      className={`w-full border-2 ${
                        errors.course_id ? "border-red-400" : "border-gray-200"
                      } rounded-xl px-4 py-4 pl-12 focus:outline-none transition-all duration-300 bg-gray-50 focus:bg-white`}
                      style={{
                        focusBorderColor: "var(--color-director)",
                        fontSize: "16px",
                      }}
                      required
                      variants={inputVariants}
                      whileHover="hover"
                      whileFocus="focus"
                    >
                      <option value="">Select Course</option>
                      {courses.map((course) => (
                        <option key={course.course_id} value={course.course_id}>
                          {course.course_name}
                        </option>
                      ))}
                    </motion.select>
                    <FiBook className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  </div>
                  {errors.course_id && (
                    <motion.p
                      className="text-red-500 text-sm flex items-center"
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                    >
                      <span className="mr-1">⚠</span> {errors.course_id}
                    </motion.p>
                  )}
                </motion.div>

                {/* Subject */}
                <motion.div variants={itemVariants} className="space-y-2">
                  <label className="flex items-center text-sm font-semibold text-gray-700 mb-2">
                    <FiBook className="mr-2" style={{ color: "var(--color-director)" }} />
                    Subject
                  </label>
                  <div className="relative">
                    <motion.select
                      value={kotaTeacherForm.subject_id}
                      onChange={(e) => handleInputChange("subject_id", e.target.value)}
                      className={`w-full border-2 ${
                        errors.subject_id ? "border-red-400" : "border-gray-200"
                      } rounded-xl px-4 py-4 pl-12 focus:outline-none transition-all duration-300 bg-gray-50 focus:bg-white`}
                      style={{
                        focusBorderColor: "var(--color-director)",
                        fontSize: "16px",
                      }}
                      required
                      variants={inputVariants}
                      whileHover="hover"
                      whileFocus="focus"
                    >
                      <option value="">Select Subject</option>
                      {subjects.map((subject) => (
                        <option key={subject.id || subject._id} value={subject.id || subject._id}>
                          {subject.name || subject.subject_name}
                        </option>
                      ))}
                    </motion.select>
                    <FiBook className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  </div>
                  {errors.subject_id && (
                    <motion.p
                      className="text-red-500 text-sm flex items-center"
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                    >
                      <span className="mr-1">⚠</span> {errors.subject_id}
                    </motion.p>
                  )}
                </motion.div>
              </div>

              {/* Password - Full Width */}
              <motion.div variants={itemVariants} className="space-y-2">
                <label className="flex items-center text-sm font-semibold text-gray-700 mb-2">
                  <FiLock className="mr-2" style={{ color: "var(--color-director)" }} />
                  Password
                </label>
                <div className="relative">
                  <motion.input
                    type={showPassword ? "text" : "password"}
                    placeholder="Enter secure password"
                    value={kotaTeacherForm.password}
                    onChange={(e) => handleInputChange("password", e.target.value)}
                    className={`w-full border-2 ${
                      errors.password ? "border-red-400" : "border-gray-200"
                    } rounded-xl px-4 py-4 pl-12 pr-12 focus:outline-none transition-all duration-300 bg-gray-50 focus:bg-white`}
                    style={{
                      focusBorderColor: "var(--color-director)",
                      fontSize: "16px",
                    }}
                    required
                    variants={inputVariants}
                    whileHover="hover"
                    whileFocus="focus"
                    aria-describedby="password-error"
                  />
                  <FiLock className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <motion.button
                    type="button"
                    onClick={togglePasswordVisibility}
                    className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700 p-1 rounded-full hover:bg-gray-100"
                    aria-label={showPassword ? "Hide password" : "Show password"}
                    variants={iconVariants}
                    whileHover="hover"
                    whileTap="tap"
                  >
                    {showPassword ? <FiEyeOff size={20} /> : <FiEye size={20} />}
                  </motion.button>
                </div>
                {errors.password && (
                  <motion.p
                    id="password-error"
                    className="text-red-500 text-sm flex items-center"
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                  >
                    <span className="mr-1">⚠</span> {errors.password}
                  </motion.p>
                )}
              </motion.div>

              {/* Submit Button */}
              <motion.div variants={itemVariants} className="pt-4">
                <motion.button
                  type="submit"
                  disabled={loading}
                  className="w-full px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-3"
                  style={{
                    background: `linear-gradient(135deg, var(--color-director), var(--color-director))`,
                    boxShadow: "0px 4px 15px rgba(0, 0, 0, 0.1)",
                    color: "white", // Fixed color to white for better contrast
                  }}
                  variants={buttonVariants}
                  whileHover="hover"
                  whileTap="tap"
                >
                  {loading ? (
                    <>
                      <motion.div
                        className="w-5 h-5 border-2 border-white border-t-transparent rounded-full"
                        animate={{ rotate: 360 }}
                        transition={{
                          duration: 1,
                          repeat: Number.POSITIVE_INFINITY,
                          ease: "linear",
                        }}
                      />
                      <span>Adding Teacher...</span>
                    </>
                  ) : (
                    <>
                      <FiUserPlus size={20} />
                      <span>Add Kota Teacher</span>
                    </>
                  )}
                </motion.button>
              </motion.div>
            </form>
          </div>
        </motion.div>
      </motion.div>
    </div>
  );
};

export default AddKotaTeachers;