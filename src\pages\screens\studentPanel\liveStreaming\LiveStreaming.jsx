import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Video, Loader2 } from 'lucide-react';

const LiveStreaming = () => {
  const [isLoading, setIsLoading] = useState(false);

  const handleRequest = () => {
    setIsLoading(true);
    // Simulate a request with a 3-second delay
    setTimeout(() => {
      setIsLoading(false);
    }, 3000);
  };

  // Animation variants for the card
  const cardVariants = {
    hidden: { opacity: 0, y: 50, scale: 0.95 },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: { duration: 0.7, ease: 'easeOut' }
    }
  };

  // Animation variants for the button
  const buttonVariants = {
    idle: { scale: 1, backgroundColor: '#4F46E5', boxShadow: '0px 0px 0px rgba(0, 0, 0, 0)' },
    loading: {
      scale: [1, 1.05, 1],
      backgroundColor: '#7C3AED',
      boxShadow: '0px 0px 15px rgba(124, 58, 237, 0.5)',
      transition: {
        scale: { repeat: Infinity, duration: 0.8 },
        boxShadow: { repeat: Infinity, duration: 0.8 }
      }
    },
    hover: {
      scale: 1.1,
      boxShadow: '0px 6px 20px rgba(79, 70, 229, 0.4)',
      transition: { duration: 0.3 }
    },
    tap: { scale: 0.95 }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-blue-50 to-purple-50 flex justify-center items-center p-4 sm:p-8">
      <motion.div
        variants={cardVariants}
        initial="hidden"
        animate="visible"
        className="bg-white/90 backdrop-blur-xl shadow-2xl rounded-3xl max-w-sm w-full p-6 sm:p-8 border border-indigo-100/50">
        {/* Header */}
        <header className="text-center mb-8">
              <Video className="h-12 w-12 text-indigo-600 animate-pulse" />
          <motion.div
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="flex items-center justify-center gap-3">
        
            <h1 className="text-3xl sm:text-4xl font-extrabold text-gray-900">Live Streaming</h1>
          </motion.div>
          <p className="text-gray-600 mt-3 text-lg sm:text-xl">
            Join our interactive live sessions
          </p>
        </header>

        {/* Request Section */}
        <section className="text-center">
          <AnimatePresence mode="wait">
            {isLoading ? (
              <motion.div
                key="loading"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                transition={{ duration: 0.3 }}
                className="flex items-center justify-center gap-3">
                <Loader2 className="h-8 w-8 text-indigo-500 animate-spin" />
                <p className="text-lg text-gray-700">Requesting Live Stream...</p>
              </motion.div>
            ) : (
              <motion.button
                key="request"
                variants={buttonVariants}
                initial="idle"
                animate="idle"
                whileHover="hover"
                whileTap="tap"
                className="bg-[var(--color-student)] hover:cursor-pointer text-white px-6 py-3 rounded-full text-lg font-medium flex items-center gap-3 mx-auto shadow-md"
                onClick={handleRequest}>
                <Video className="h-6 w-6" />
                Request Live Stream
              </motion.button>
            )}
          </AnimatePresence>
        </section>
      </motion.div>
    </div>
  );
};

export default LiveStreaming;
