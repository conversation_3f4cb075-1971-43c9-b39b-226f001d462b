from flask import Flask, request, jsonify
from flask_cors import CORS
import sys
import os

# Add parent directory to path to import shared modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from shared.database import Database
from shared.auth_middleware import token_required

app = Flask(__name__)
CORS(app)

db = Database()

@app.route('/student-dashboard', methods=['GET'])
@token_required(['student'])
def get_student_dashboard():
    try:
        student_id = request.current_user['id']  # This is now a UUID string
        
        # Get student info with center details
        student = db.execute_query_one(
            """SELECT s.*, c.name as center_name, c.email as center_email, c.phone as center_phone
               FROM students s
               JOIN centers c ON s.center_code = c.center_code
               WHERE s.id = %s""",
            (student_id,)
        )
        
        if not student:
            return jsonify({'message': 'Student not found'}), 404
        
        # Get parent info
        parent = db.execute_query_one(
            "SELECT first_name, last_name, email, phone, relationship FROM parents WHERE student_id = %s",
            (student_id,)
        )
        
        # Get center faculty
        faculty = db.execute_query(
            """SELECT id, first_name, last_name, email, phone
               FROM faculty 
               WHERE center_code = %s AND is_active = TRUE""",
            (student['center_code'],),
            fetch=True
        )
        
        # Get mapped kota teachers
        kota_teachers = db.execute_query(
            """SELECT kt.id, kt.first_name, kt.last_name, kt.course
               FROM kota_teachers kt
               JOIN teacher_center_mapping tcm ON kt.id = tcm.teacher_id
               WHERE tcm.center_code = %s AND kt.is_active = TRUE""",
            (student['center_code'],),
            fetch=True
        )
        
        # Convert UUIDs to strings for JSON response
        if student:
            student['id'] = str(student['id'])
        
        if faculty:
            for f in faculty:
                f['id'] = str(f['id'])
        
        if kota_teachers:
            for kt in kota_teachers:
                kt['id'] = str(kt['id'])
        
        return jsonify({
            'student': student,
            'parent': parent,
            'faculty': faculty,
            'kota_teachers': kota_teachers
        }), 200
        
    except Exception as e:
        print(f"Student dashboard error: {e}")
        return jsonify({'message': 'Internal server error'}), 500

@app.route('/parent-dashboard', methods=['GET'])
@token_required(['parent'])
def get_parent_dashboard():
    try:
        parent_id = request.current_user['id']  # This is now a UUID string
        
        # Get parent info
        parent = db.execute_query_one(
            "SELECT * FROM parents WHERE id = %s",
            (parent_id,)
        )
        
        if not parent:
            return jsonify({'message': 'Parent not found'}), 404
        
        # Get student info with center details
        student = db.execute_query_one(
            """SELECT s.*, c.name as center_name, c.email as center_email, c.phone as center_phone
               FROM students s
               JOIN centers c ON s.center_code = c.center_code
               WHERE s.id = %s""",
            (parent['student_id'],)
        )
        
        # Get center info explicitly
        center = db.execute_query_one(
            """SELECT name, email, phone, center_code
               FROM centers 
               WHERE center_code = %s""",
            (student['center_code'],)
        )
        
        # Get center faculty
        faculty = db.execute_query(
            """SELECT id, first_name, last_name, email, phone
               FROM faculty 
               WHERE center_code = %s AND is_active = TRUE""",
            (student['center_code'],),
            fetch=True
        )
        
        # Get mapped kota teachers
        kota_teachers = db.execute_query(
            """SELECT kt.id, kt.first_name, kt.last_name, kt.course, kt.subject
               FROM kota_teachers kt
               JOIN teacher_center_mapping tcm ON kt.id = tcm.teacher_id
               WHERE tcm.center_code = %s AND kt.is_active = TRUE""",
            (student['center_code'],),
            fetch=True
        )
        
        # Convert UUIDs to strings for JSON response
        if parent:
            parent['id'] = str(parent['id'])
            parent['student_id'] = str(parent['student_id'])
        
        if student:
            student['id'] = str(student['id'])
        
        if center:
            center['center_code'] = str(center['center_code'])
        
        if faculty:
            for f in faculty:
                f['id'] = str(f['id'])
        
        if kota_teachers:
            for kt in kota_teachers:
                kt['id'] = str(kt['id'])
        
        return jsonify({
            'parent': parent,
            'student': student,
            'center': center,  # Added center to response
            'faculty': faculty,
            'kota_teachers': kota_teachers
        }), 200
        
    except Exception as e:
        print(f"Parent dashboard error: {e}")
        return jsonify({'message': 'Internal server error'}), 500

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'service': 'Student Service (UUID)',
        'message': 'Student service is running with UUID support'
    }), 200

if __name__ == '__main__':
    app.run(host='0.0.0.0',debug=True, port=8006)
