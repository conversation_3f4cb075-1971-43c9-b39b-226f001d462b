import React, { useEffect, useCallback, useState } from 'react';
// eslint-disable-next-line no-unused-vars
import { motion } from 'framer-motion';
import { useDispatch, useSelector } from 'react-redux';
import {
  setOnBroadingAssessmentStatus,
  useCheckOnBroadingAssessmentStatusMutation
} from '../onBroadingAssesment/onBroadingAssessment.slice';
// import { useGetStudentsQuery } from './students.Slice';
import OnBroadingAssessment from '../onBroadingAssesment/OnBroadingAssessment';
import { User, Home, Edit } from 'lucide-react';
// import { useLazyGetUserStudentDashboardDataQuery } from '../../Dashboard/dashboard.slice';
const StudentsDashboard = () => {
  // const [userStudentDetails] = useLazyGetUserStudentDashboardDataQuery();
  const [checkOnBroadingAssessmentStatus] = useCheckOnBroadingAssessmentStatusMutation();
  const dispatch = useDispatch();

  // Fetch student data
  // const {
  //   data: studentsData,
  //   isLoading,
  //   isError,
  //   error
  // } = useGetStudentsQuery(undefined, {
  //   skip: !sessionStorage.getItem('userId') // Skip if no userId
  // });

  const onBroadingAssessmentData = useSelector(
    (state) => state.onBroadingAssessment.onBroadingAssessmentStatus
  );

  const [isSkipped, setIsSkipped] = useState(() => sessionStorage.getItem('isSkip') === 'true');

  const handleCheckAssessmentStatus = useCallback(async () => {
    try {
      const studentId = sessionStorage.getItem('userId');
      if (!studentId) return;

      const res = await checkOnBroadingAssessmentStatus({
        student_id: studentId
      }).unwrap();

      const sessionSkip = sessionStorage.getItem('isSkip');

      if (res.assessment_required && !res.assessment_completed && sessionSkip !== 'true') {
        sessionStorage.setItem('isSkip', 'false');
        setIsSkipped(false);
      }

      dispatch(setOnBroadingAssessmentStatus(res));
    } catch (error) {
      console.error('Error checking assessment status:', error);
    }
  }, [checkOnBroadingAssessmentStatus, dispatch]);

  useEffect(() => {
    handleCheckAssessmentStatus();
  }, [handleCheckAssessmentStatus]);

  const handleSkip = () => {
    if (sessionStorage.getItem('isSkip') !== 'true') {
      sessionStorage.setItem('isSkip', 'true');
      setIsSkipped(true);
    }
  };

  // const userDetails = async () => {
  //   try {
  //     const response = await userStudentDetails(sessionStorage.getItem('userId')).unwrap();
  //     console.log(response);
  //   } catch (err) {
  //     console.error('Error fetching user details:', err);
  //   }
  // };

  const shouldShowAssessment =
    onBroadingAssessmentData?.assessment_required &&
    !onBroadingAssessmentData?.assessment_completed &&
    !isSkipped;

  const user = {
    username: sessionStorage.getItem('name') || 'Unknown',
    centercode: sessionStorage.getItem('centercode') || 'Unknown',
    role: sessionStorage.getItem('role') || 'Unknown',
    phone: sessionStorage.getItem('phone') || 'Unknown'
  };

  // Dummy data for dashboard widgets
  const dashboardWidgets = [
    {
      id: 1,
      title: '24/7 Chatbot Support',
      stat: '150+ Queries',
      description: 'Get instant help anytime with our AI chatbot!',
      icon: '💬',
      color: 'bg-[var(--color-student)]/10 border-[var(--color-student)]/20',
      buttonColor: 'bg-[var(--color-student)] hover:bg-[var(--color-student)]/90'
    },
    {
      id: 2,
      title: 'E-Book Center',
      stat: '200+ Books',
      description: 'Access a vast library of e-books for learning.',
      icon: '📚',
      color: 'bg-[var(--color-student)]/10 border-[var(--color-student)]/20',
      buttonColor: 'bg-[var(--color-student)] hover:bg-[var(--color-student)]/90'
    },
    {
      id: 3,
      title: 'Recordings',
      stat: '75 Videos',
      description: 'Watch recorded lectures at your convenience.',
      icon: '🎥',
      color: 'bg-[var(--color-student)]/10 border-[var(--color-student)]/20',
      buttonColor: 'bg-[var(--color-student)] hover:bg-[var(--color-student)]/90'
    },
    {
      id: 4,
      title: 'Create Your Own Test',
      stat: '50 Tests',
      description: 'Design custom tests to assess your skills.',
      icon: '📝',
      color: 'bg-[var(--color-student)]/10 border-[var(--color-student)]/20',
      buttonColor: 'bg-[var(--color-student)] hover:bg-[var(--color-student)]/90'
    },
    {
      id: 5,
      title: 'Booster Module',
      stat: '30 Exercises',
      description: 'Boost your performance with targeted exercises.',
      icon: '🚀',
      color: 'bg-[var(--color-student)]/10 border-[var(--color-student)]/20',
      buttonColor: 'bg-[var(--color-student)] hover:bg-[var(--color-student)]/90'
    },
    {
      id: 6,
      title: 'AI Tutor',
      stat: '100+ Sessions',
      description: 'Personalized learning with an AI tutor.',
      icon: '🤖',
      color: 'bg-[var(--color-student)]/10 border-[var(--color-student)]/20',
      buttonColor: 'bg-[var(--color-student)] hover:bg-[var(--color-student)]/90'
    }
  ];

  return (
    <div
      className="min-h-screen bg-gradient-to-br from-gray-50 via-[var(--color-student)]/5 to-gray-50 p-6"
      style={{ '--color-student': '#2563eb' }}>
      {shouldShowAssessment && <OnBroadingAssessment isSkip={handleSkip} />}
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, ease: 'easeOut' }}
        className="max-w-7xl mx-auto space-y-8">
        {/* Header */}
        {/* <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2, duration: 0.6 }}
          className="text-center mb-12"
        >
          <motion.h1
            className="text-5xl font-extrabold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-[var(--color-student)] to-[var(--color-student)]/70"
          >
            🎓 Student Dashboard
          </motion.h1>
          <p className="text-lg text-gray-600">
            Welcome! Track your progress and explore resources.
          </p>
        </motion.div> */}

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4, duration: 0.6 }}
          className="text-center mt-12">
          <h2 className="text-3xl font-bold text-[var(--color-student)]">Hello Student!</h2>
          <p className="text-gray-500 mt-2">Get started with your learning journey today.</p>
        </motion.div>

        {/* User Details */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="bg-white rounded-xl p-6 shadow-lg border-l-4 border-[var(--color-student)]/20 mb-8">
          <h2 className="text-2xl font-semibold text-[var(--color-student)] mb-4">User Profile</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <p className="text-gray-600">
                <strong>Username:</strong> {user.username}
              </p>
            </div>

            {/* Profile info grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-5 ">
              {/* Username card */}
              <motion.div
                whileHover={{ y: -3 }}
                className="bg-white/90 backdrop-blur-sm p-4 rounded-xl border border-[var(--color-student)]/20 shadow-sm">
                <div className="flex items-center gap-2 text-sm text-gray-500 mb-1">
                  <User className="h-4 w-4" />
                  <span>Username</span>
                </div>
                <p className="text-lg font-medium text-gray-800">{user.username}</p>
              </motion.div>

              {/* Center code card */}
              <motion.div
                whileHover={{ y: -3 }}
                className="bg-white/90 backdrop-blur-sm p-4 rounded-xl border border-[var(--color-student)]/20 shadow-sm">
                <div className="flex items-center gap-2 text-sm text-gray-500 mb-1">
                  <Home className="h-4 w-4" />
                  <span>Center Code</span>
                </div>
                <p className="text-lg font-medium text-gray-800">{user.centercode}</p>
              </motion.div>

              {/* Additional fields would go here with same styling */}
            </div>
          </div>
        </motion.div>

        {/* Dashboard Widgets */}
        <motion.div
          initial="hidden"
          animate="visible"
          variants={{
            hidden: { opacity: 0 },
            visible: {
              opacity: 1,
              transition: { staggerChildren: 0.1, delayChildren: 0.2 }
            }
          }}
          className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {dashboardWidgets.map((widget) => (
            <motion.div
              key={widget.id}
              variants={{
                hidden: { opacity: 0, y: 20 },
                visible: { opacity: 1, y: 0 }
              }}
              whileHover={{ scale: 1.03, transition: { duration: 0.3 } }}
              className={`rounded-xl p-6 shadow-md ${widget.color} hover:shadow-lg transition-all duration-300`}>
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-800">{widget.title}</h3>
                <span className="text-2xl">{widget.icon}</span>
              </div>
              <p className="text-2xl font-bold text-[var(--color-student)] mb-2">{widget.stat}</p>
              <p className="text-sm text-gray-600 mb-4">{widget.description}</p>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className={`px-4 py-2 rounded-full text-white text-sm font-medium ${widget.buttonColor} transition-colors`}>
                {widget.buttonText}
              </motion.button>
            </motion.div>
          ))}
        </motion.div>

        {/* Welcome Message */}
      </motion.div>
    </div>
  );
};

export default StudentsDashboard;
