
import { useEffect, useState, useRef } from "react"
import { Room, RoomEvent } from "livekit-client"
import io from "socket.io-client"
import {
  useLazyGetCenterLiveViewerQuery,
  useJoinLiveStreamMutation,
  useStartTranslationSessionMutation,
  useStopTranslationSessionMutation,
  useSendChatMessageMutation,
  useLazyGetChatHistoryQuery,
} from "./centerTraineeLive.slice"

const CenterTraineeLiveViewer = () => {
  const [trigger, { data, error, isLoading }] = useLazyGetCenterLiveViewerQuery()
  const [joinStream, { isLoading: isJoining }] = useJoinLiveStreamMutation()
  const [startTranslationSession] = useStartTranslationSessionMutation()
  const [stopTranslationSession] = useStopTranslationSessionMutation()
  const [sendChatMessage, { isLoading: isSendingMessage }] = useSendChatMessageMutation()
  const [getChatHistory, { data: chatHistoryData, isLoading: isLoadingHistory }] = useLazyGetChatHistoryQuery()

  // Stream viewing states
  const [isViewingStream, setIsViewingStream] = useState(false)
  const [currentStream, setCurrentStream] = useState(null)
  const [livekitRoom, setLivekitRoom] = useState(null)
  const [livekitConnected, setLivekitConnected] = useState(false)
  const [participants, setParticipants] = useState([])
  const [remoteVideoTracks, setRemoteVideoTracks] = useState([])
  const [remoteAudioTracks, setRemoteAudioTracks] = useState([])
  const [connectionStatus, setConnectionStatus] = useState("")
  const [hasScreenShare, setHasScreenShare] = useState(false)
  const [hasCameraTrack, setHasCameraTrack] = useState(false)

  // Refs for video elements
  const mainVideoRef = useRef(null)
  const screenVideoRef = useRef(null)
  const cameraVideoRef = useRef(null)
  const cameraMainRef = useRef(null)

  // Translation states
  const [isTranslationEnabled, setIsTranslationEnabled] = useState(false)
  const [translationSession, setTranslationSession] = useState(null)
  const [translationWebSocket, setTranslationWebSocket] = useState(null)
  const [sourceLanguage, setSourceLanguage] = useState("en")
  const [targetLanguage, setTargetLanguage] = useState("ta")
  const [translationStatus, setTranslationStatus] = useState("")
  const [currentTranscription, setCurrentTranscription] = useState("")
  const [currentTranslation, setCurrentTranslation] = useState("")
  const [isPlayingTranslatedAudio, setIsPlayingTranslatedAudio] = useState(false)
  const [audioQueueLength, setAudioQueueLength] = useState(0)

  // Translation audio refs
  const audioContextRef = useRef(null)
  const mediaRecorderRef = useRef(null)
  const translationWebSocketRef = useRef(null)

  // Audio Queue System refs and state
  const audioQueueRef = useRef([])
  const currentAudioSourceRef = useRef(null)
  const currentHtmlAudioRef = useRef(null)
  const isProcessingQueueRef = useRef(false)

  // Chat states
  const [chatMessages, setChatMessages] = useState([])
  const [newMessage, setNewMessage] = useState("")
  const [isChatOpen, setIsChatOpen] = useState(false)
  const [unreadMessages, setUnreadMessages] = useState(0)

  // Socket.IO states
  const [socket, setSocket] = useState(null)
  const [socketConnected, setSocketConnected] = useState(false)

  // Available languages
  const availableLanguages = [
    { code: "en", name: "English", flag: "🇺🇸" },
    { code: "ta", name: "Tamil", flag: "🇮🇳" },
    { code: "hi", name: "Hindi", flag: "🇮🇳" },
    { code: "te", name: "Telugu", flag: "🇮🇳" },
    { code: "kn", name: "Kannada", flag: "🇮🇳" },
  ]

  // Mute/unmute original audio based on translation state
  useEffect(() => {
    remoteAudioTracks.forEach(({ audioElement }) => {
      if (audioElement) {
        audioElement.muted = isTranslationEnabled
      }
    })
  }, [isTranslationEnabled, remoteAudioTracks])

  useEffect(() => {
    trigger()
    const interval = setInterval(() => trigger(), 30000)
    return () => clearInterval(interval)
  }, [trigger])

  useEffect(() => {
    return () => {
      if (livekitRoom) {
        livekitRoom.disconnect()
        setLivekitRoom(null)
      }
      cleanupTranslation()
    }
  }, [livekitRoom])

  // HTTP-based chat system (more reliable than Socket.IO)
  useEffect(() => {
    if (isViewingStream && currentStream) {
      console.log("💬 VIEWER: Starting HTTP-based chat for session:", currentStream.session_id)
      setSocketConnected(true) // Mark as "connected" for UI purposes

      // Load initial chat history
      loadChatHistory()

      // Start polling for new messages every 2 seconds
      const pollInterval = setInterval(() => {
        loadChatHistory()
      }, 2000)

      return () => {
        clearInterval(pollInterval)
        setSocketConnected(false)
      }
    }
  }, [isViewingStream, currentStream])

  // Load chat history using slice
  const loadChatHistory = async () => {
    if (!currentStream?.session_id) return

    try {
      console.log(`🔍 VIEWER: Loading chat history for session: ${currentStream.session_id}`)
      const response = await getChatHistory(currentStream.session_id).unwrap()
      const newMessages = response.messages || []
      console.log(`🔍 VIEWER: API returned ${newMessages.length} messages for session ${currentStream.session_id}`)

      setChatMessages((prev) => {
        // Only update if there are new messages
        if (newMessages.length !== prev.length) {
          console.log(`💬 VIEWER: Updated chat with ${newMessages.length} messages`)
          // Check for new messages to increment unread count
          const newCount = newMessages.length - prev.length
          if (newCount > 0 && !isChatOpen) {
            setUnreadMessages((prev) => prev + newCount)
          }
          return newMessages
        }
        return prev
      })
    } catch (error) {
      console.log("❌ VIEWER: Failed to load chat history:", error.message)
    }
  }

  // Audio Queue Management Functions
  const addToAudioQueue = (audioData) => {
    console.log("📥 Adding audio to queue, current queue length:", audioQueueRef.current.length)
    audioQueueRef.current.push(audioData)
    setAudioQueueLength(audioQueueRef.current.length)
    processAudioQueue()
  }

  const processAudioQueue = async () => {
    if (isProcessingQueueRef.current || audioQueueRef.current.length === 0) {
      return
    }

    isProcessingQueueRef.current = true
    console.log("🎵 Processing audio queue, items:", audioQueueRef.current.length)

    while (audioQueueRef.current.length > 0) {
      const audioData = audioQueueRef.current.shift()
      setAudioQueueLength(audioQueueRef.current.length)
      await playAudioFromQueue(audioData)
    }

    isProcessingQueueRef.current = false
    setAudioQueueLength(0)
    console.log("✅ Audio queue processing completed")
  }

  const stopCurrentAudio = () => {
    // Stop Web Audio API source
    if (currentAudioSourceRef.current) {
      try {
        currentAudioSourceRef.current.stop()
        currentAudioSourceRef.current.disconnect()
      } catch (error) {
        console.warn("⚠️ Error stopping Web Audio source:", error)
      }
      currentAudioSourceRef.current = null
    }

    // Stop HTML5 Audio
    if (currentHtmlAudioRef.current) {
      try {
        currentHtmlAudioRef.current.pause()
        currentHtmlAudioRef.current.currentTime = 0
        if (currentHtmlAudioRef.current.src) {
          URL.revokeObjectURL(currentHtmlAudioRef.current.src)
        }
      } catch (error) {
        console.warn("⚠️ Error stopping HTML5 Audio:", error)
      }
      currentHtmlAudioRef.current = null
    }

    setIsPlayingTranslatedAudio(false)
  }

  const clearAudioQueue = () => {
    console.log("🗑️ Clearing audio queue")
    audioQueueRef.current = []
    setAudioQueueLength(0)
    stopCurrentAudio()
    isProcessingQueueRef.current = false
  }

  const cleanupTranslation = () => {
    if (translationWebSocket) {
      translationWebSocket.close()
      setTranslationWebSocket(null)
    }

    if (translationWebSocketRef.current) {
      translationWebSocketRef.current.close()
      translationWebSocketRef.current = null
    }

    if (mediaRecorderRef.current) {
      if (mediaRecorderRef.current.disconnect) {
        mediaRecorderRef.current.disconnect()
      } else if (mediaRecorderRef.current.stop) {
        mediaRecorderRef.current.stop()
      }
      if (mediaRecorderRef.current.abort) {
        mediaRecorderRef.current.abort()
      }
      mediaRecorderRef.current = null
    }

    if (audioContextRef.current && audioContextRef.current.state !== "closed") {
      audioContextRef.current.close()
      audioContextRef.current = null
    }

    // Clear audio queue and stop any playing audio
    clearAudioQueue()

    setTranslationSession(null)
    setIsTranslationEnabled(false)
    setTranslationStatus("")
    setCurrentTranscription("")
    setCurrentTranslation("")
  }

  useEffect(() => {
    if (remoteVideoTracks.length === 0) return

    remoteVideoTracks.forEach(({ track, publication }) => {
      if (publication.source === "screen_share" || publication.source === "screen_share_audio") {
        if (screenVideoRef.current && !track.attachedElements.includes(screenVideoRef.current)) {
          track.attach(screenVideoRef.current)
        }
      } else if (publication.source === "camera" || publication.trackName === "teacher_camera" || !publication.source) {
        if (hasScreenShare) {
          if (cameraVideoRef.current && !track.attachedElements.includes(cameraVideoRef.current)) {
            track.attach(cameraVideoRef.current)
          }
        } else {
          if (cameraMainRef.current && !track.attachedElements.includes(cameraMainRef.current)) {
            track.attach(cameraMainRef.current)
          }
        }
      }
    })
  }, [remoteVideoTracks, hasScreenShare, hasCameraTrack])

  const connectToLiveKitRoom = async (token, url) => {
    try {
      setConnectionStatus("Connecting...")
      const room = new Room()

      room.on(RoomEvent.Connected, () => {
        setLivekitConnected(true)
        setConnectionStatus("Connected")
      })

      room.on(RoomEvent.Disconnected, () => {
        setLivekitConnected(false)
        setConnectionStatus("Disconnected")
        setIsViewingStream(false)
      })

      room.on(RoomEvent.ParticipantConnected, (participant) => {
        setParticipants((prev) => [...prev, participant])
      })

      room.on(RoomEvent.ParticipantDisconnected, (participant) => {
        setParticipants((prev) => prev.filter((p) => p.identity !== participant.identity))
      })

      room.on(RoomEvent.TrackSubscribed, (track, publication, participant) => {
        if (track.kind === "video") {
          setRemoteVideoTracks((prev) => {
            const newTracks = [...prev, { track, participant, publication }]
            setHasScreenShare(
              newTracks.some(
                (t) => t.publication.source === "screen_share" || t.publication.source === "screen_share_audio",
              ),
            )
            setHasCameraTrack(
              newTracks.some(
                (t) =>
                  t.publication.source === "camera" ||
                  t.publication.trackName === "teacher_camera" ||
                  !t.publication.source,
              ),
            )
            return newTracks
          })

          if (publication.source === "screen_share" || publication.source === "screen_share_audio") {
            if (screenVideoRef.current) track.attach(screenVideoRef.current)
          } else if (
            publication.source === "camera" ||
            publication.trackName === "teacher_camera" ||
            !publication.source
          ) {
            if (cameraVideoRef.current) track.attach(cameraVideoRef.current)
            if (
              !remoteVideoTracks.some(
                (t) => t.publication.source === "screen_share" || t.publication.source === "screen_share_audio",
              ) &&
              cameraMainRef.current
            ) {
              track.attach(cameraMainRef.current)
            }
          }
        } else if (track.kind === "audio") {
          const audioElement = track.attach()
          setRemoteAudioTracks((prev) => [...prev, { track, participant, publication, audioElement }])
        }
      })

      room.on(RoomEvent.TrackUnsubscribed, (track, publication, participant) => {
        if (track.kind === "video") {
          setRemoteVideoTracks((prev) => {
            const newTracks = prev.filter((t) => t.track !== track)
            setHasScreenShare(
              newTracks.some(
                (t) => t.publication.source === "screen_share" || t.publication.source === "screen_share_audio",
              ),
            )
            setHasCameraTrack(
              newTracks.some(
                (t) =>
                  t.publication.source === "camera" ||
                  t.publication.trackName === "teacher_camera" ||
                  !t.publication.source,
              ),
            )
            return newTracks
          })
          track.detach()
        } else if (track.kind === "audio") {
          setRemoteAudioTracks((prev) => {
            const trackInfo = prev.find((t) => t.track === track)
            if (trackInfo && trackInfo.audioElement) {
              track.detach(trackInfo.audioElement)
            }
            return prev.filter((t) => t.track !== track)
          })
        }
      })

      await room.connect(url, token)
      setLivekitRoom(room)
    } catch (err) {
      setConnectionStatus("Connection failed: " + err.message)
      alert("Failed to connect to stream: " + err.message)
    }
  }

  const handleJoinStream = async (stream) => {
    try {
      const userId = sessionStorage.getItem("userId")
      if (!userId) {
        alert("Please login first to join the stream.")
        return
      }

      if (!stream.session_id || !stream.teacher_id) {
        alert("Invalid stream data. Please try again.")
        return
      }

      const sessionData = { session_id: stream.session_id, teacher_id: stream.teacher_id }
      const response = await joinStream(sessionData).unwrap()

      console.log("🔍 JOIN STREAM RESPONSE:", response)

      if (response.token && response.livekit_url) {
        // CRITICAL FIX: Always use the session_id from the response, not the original stream
        const correctSessionId = response.stream_info?.session_id || response.room_name || stream.session_id

        const updatedStream = {
          ...stream,
          session_id: correctSessionId, // Use the correct session ID from backend
        }

        console.log("🔍 ORIGINAL STREAM SESSION ID:", stream.session_id)
        console.log("🔍 BACKEND RESPONSE SESSION ID:", correctSessionId)
        console.log("🔍 FINAL STREAM FOR VIEWER:", updatedStream)

        // Ensure we're using the correct session ID
        if (stream.session_id !== correctSessionId) {
          console.log("⚠️ SESSION ID MISMATCH DETECTED!")
          console.log(`⚠️ Original: ${stream.session_id}`)
          console.log(`⚠️ Correct: ${correctSessionId}`)
          console.log("✅ Using correct session ID from backend response")

          // Show alert to user about the fix
          alert(
            `🔧 Session ID Fixed!\nOriginal: ${stream.session_id}\nCorrected: ${correctSessionId}\n\nChat should now work properly!`,
          )
        }

        setCurrentStream(updatedStream)
        setIsViewingStream(true)
        await connectToLiveKitRoom(response.token, response.livekit_url)
      } else {
        alert("Invalid stream response. Missing connection credentials.")
      }
    } catch (error) {
      alert("Failed to join stream: " + (error.data?.message || "Unknown error"))
    }
  }

  const handleLeaveStream = () => {
    if (livekitRoom) {
      livekitRoom.disconnect()
      setLivekitRoom(null)
    }

    cleanupTranslation()
    setIsViewingStream(false)
    setCurrentStream(null)
    setLivekitConnected(false)
    setParticipants([])
    setRemoteVideoTracks([])
    setRemoteAudioTracks([])
    setConnectionStatus("")
    setHasScreenShare(false)
    setHasCameraTrack(false)
  }

  const startTranslation = async () => {
    try {
      setTranslationStatus("Starting translation...")
      const userId = sessionStorage.getItem("userId")
      if (!userId) throw new Error("User not authenticated")

      const data = await startTranslationSession({
        user_id: userId,
        stream_session_id: currentStream.session_id,
        source_language: sourceLanguage,
        target_language: targetLanguage,
      }).unwrap()

      setTranslationSession(data.session_id)
      await connectTranslationWebSocket(data.websocket_url)
      await new Promise((resolve) => setTimeout(resolve, 2000))

      const ws = translationWebSocketRef.current || translationWebSocket
      if (!ws || ws.readyState !== WebSocket.OPEN) throw new Error("WebSocket not ready")

      await startAudioCapture()
      setIsTranslationEnabled(true)
      setTranslationStatus("Translation active - listening for audio")
    } catch (error) {
      setTranslationStatus(`Error: ${error.message}`)
    }
  }

  const stopTranslation = async () => {
    try {
      setTranslationStatus("Stopping translation...")
      if (translationSession) {
        await stopTranslationSession({ session_id: translationSession }).unwrap()
      }
      cleanupTranslation()
      setTranslationStatus("Translation stopped")
    } catch (error) {
      setTranslationStatus(`Error: ${error.message}`)
    }
  }

  const connectTranslationWebSocket = async (websocketUrl) => {
    return new Promise((resolve, reject) => {
      const ws = new WebSocket(websocketUrl)

      ws.onopen = () => {
        setTranslationWebSocket(ws)
        translationWebSocketRef.current = ws
        setTranslationStatus("WebSocket connected - ready for audio")
        resolve()
      }

      ws.onmessage = (event) => {
        const data = JSON.parse(event.data)
        handleTranslationMessage(data)
      }

      ws.onerror = (error) => {
        setTranslationStatus(`WebSocket error: ${error.message || "Connection failed"}`)
        reject(error)
      }

      ws.onclose = () => {
        setTranslationWebSocket(null)
        setTranslationStatus("WebSocket disconnected")
      }
    })
  }

  const handleTranslationMessage = (data) => {
    console.log("📨 Received WebSocket message:", JSON.stringify(data))
    console.log("🔄 Handling translation message:", data.type, data)

    switch (data.type) {
      case "connection_established":
        setTranslationStatus("Connected and ready")
        break
      case "transcription":
        setCurrentTranscription(data.text)
        setTranslationStatus("Transcribing...")
        break
      case "translation":
        console.log("🌐 Received translation:", data.translated_text)
        setCurrentTranslation(data.translated_text)
        setTranslationStatus("Translating...")
        break
      case "translated_audio":
        console.log("🔊 Received translated audio, length:", data.audio_data?.length)
        setTranslationStatus("Queuing audio...")
        addToAudioQueue(data.audio_data)
        break
      case "error":
        setTranslationStatus(`Error: ${data.message}`)
        break
      default:
        console.log("❓ Unknown message type:", data.type)
        break
    }
  }

  const startAudioCapture = async () => {
    try {
      setTranslationStatus("Starting audio capture...")

      if (remoteAudioTracks.length === 0) throw new Error("No audio tracks available")

      const audioTrack = remoteAudioTracks[0].track
      const mediaStream = new MediaStream([audioTrack.mediaStreamTrack])

      const audioContext = new (window.AudioContext || window.webkitAudioContext)({ sampleRate: 16000 })
      audioContextRef.current = audioContext

      const source = audioContext.createMediaStreamSource(mediaStream)
      const bufferSize = 4096
      const processor = audioContext.createScriptProcessor(bufferSize, 1, 1)

      let audioBuffer = []
      let bufferDuration = 0
      const targetDuration = 2
      const sampleRate = audioContext.sampleRate

      processor.onaudioprocess = (event) => {
        const inputData = event.inputBuffer.getChannelData(0)
        audioBuffer.push(new Float32Array(inputData))
        bufferDuration += inputData.length / sampleRate

        if (bufferDuration >= targetDuration) {
          console.log("📊 Processing", bufferDuration.toFixed(2) + "s", "of audio data")

          const totalLength = audioBuffer.reduce((sum, chunk) => sum + chunk.length, 0)
          const combinedBuffer = new Float32Array(totalLength)
          let offset = 0

          for (const chunk of audioBuffer) {
            combinedBuffer.set(chunk, offset)
            offset += chunk.length
          }

          // MODIFICATION: Convert Float32Array to Int16Array (raw PCM)
          const pcm16Data = new Int16Array(combinedBuffer.length)
          for (let i = 0; i < combinedBuffer.length; i++) {
            const sample = Math.max(-1, Math.min(1, combinedBuffer[i]))
            // Scale to 16-bit integer range
            pcm16Data[i] = sample < 0 ? sample * 0x8000 : sample * 0x7fff
          }
          console.log("🎵 Created raw PCM data:", pcm16Data.buffer.byteLength, "bytes")

          const ws = translationWebSocketRef.current || translationWebSocket
          console.log("🔍 WebSocket check:", ws)

          if (ws && ws.readyState === WebSocket.OPEN) {
            console.log("📤 Sending raw PCM audio data to translation service...")
            // Send the ArrayBuffer of the Int16Array
            ws.send(pcm16Data.buffer)
            console.log("✅ Audio data sent successfully")
          } else {
            console.error("❌ WebSocket not ready, state:", ws?.readyState)
          }

          audioBuffer = []
          bufferDuration = 0
        }
      }

      source.connect(processor)
      processor.connect(audioContext.destination)

      mediaRecorderRef.current = processor
      setTranslationStatus("Capturing audio from stream...")
    } catch (error) {
      setTranslationStatus(`Audio capture error: ${error.message}`)
      await startSpeechRecognitionFallback()
    }
  }

  const startSpeechRecognitionFallback = async () => {
    try {
      setTranslationStatus("Using speech recognition fallback...")

      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition
      if (!SpeechRecognition) throw new Error("Speech recognition not supported")

      const recognition = new SpeechRecognition()
      recognition.continuous = true
      recognition.interimResults = true
      recognition.lang =
        sourceLanguage === "en"
          ? "en-US"
          : sourceLanguage === "ta"
            ? "ta-IN"
            : sourceLanguage === "hi"
              ? "hi-IN"
              : sourceLanguage === "te"
                ? "te-IN"
                : sourceLanguage === "kn"
                  ? "kn-IN"
                  : "en-US"

      recognition.onresult = async (event) => {
        const result = event.results[event.results.length - 1]
        const transcript = result[0].transcript

        if (result.isFinal && transcript.trim()) {
          setCurrentTranscription(transcript)
          const ws = translationWebSocketRef.current || translationWebSocket
          if (ws && ws.readyState === WebSocket.OPEN) {
            ws.send(
              JSON.stringify({
                type: "transcript",
                text: transcript,
                source_language: sourceLanguage,
                target_language: targetLanguage,
                session_id: translationSession,
              }),
            )
          }
        }
      }

      recognition.onerror = (event) => console.error("Speech recognition error:", event.error)
      recognition.onend = () => {
        if (isTranslationEnabled) setTimeout(() => recognition.start(), 500)
      }

      mediaRecorderRef.current = recognition
      recognition.start()
    } catch (error) {
      setTranslationStatus(`All audio capture methods failed: ${error.message}`)
    }
  }

  // Audio playback function for queue system
  const playAudioFromQueue = async (audioDataBase64) => {
    return new Promise(async (resolve) => {
      try {
        console.log("🎵 Playing queued audio, data length:", audioDataBase64?.length)
        setTranslationStatus("Playing audio...")

        if (!audioDataBase64) {
          console.error("❌ No audio data provided")
          setIsPlayingTranslatedAudio(false)
          resolve()
          return
        }

        // Create audio context if not exists
        if (!audioContextRef.current) {
          audioContextRef.current = new (window.AudioContext || window.webkitAudioContext)()
        }

        // Resume audio context if suspended
        if (audioContextRef.current.state === "suspended") {
          await audioContextRef.current.resume()
        }

        // Method 1: Try Web Audio API with base64 decoding
        try {
          // Convert base64 to array buffer
          const binaryString = atob(audioDataBase64)
          const audioData = new Uint8Array(binaryString.length)
          for (let i = 0; i < binaryString.length; i++) {
            audioData[i] = binaryString.charCodeAt(i)
          }

          console.log("🔊 Decoded audio data, size:", audioData.length, "bytes")

          const audioBuffer = await audioContextRef.current.decodeAudioData(audioData.buffer)
          const source = audioContextRef.current.createBufferSource()
          source.buffer = audioBuffer
          source.connect(audioContextRef.current.destination)

          // Store reference to current audio source
          currentAudioSourceRef.current = source
          setIsPlayingTranslatedAudio(true)

          source.onended = () => {
            console.log("🎵 Web Audio playback ended")
            setIsPlayingTranslatedAudio(false)
            currentAudioSourceRef.current = null
            setTranslationStatus("Translation active - listening for audio")
            resolve()
          }

          source.start()
          console.log("✅ Web Audio playback started successfully")
          return
        } catch (webAudioError) {
          console.warn("⚠️ Web Audio API failed:", webAudioError.message)

          // Method 2: Fallback to HTML5 Audio element
          try {
            const audioBlob = new Blob(
              [
                new Uint8Array(
                  atob(audioDataBase64)
                    .split("")
                    .map((c) => c.charCodeAt(0)),
                ),
              ],
              { type: "audio/wav" },
            )

            const audioUrl = URL.createObjectURL(audioBlob)
            const audio = new Audio(audioUrl)

            // Store reference to current HTML audio
            currentHtmlAudioRef.current = audio
            setIsPlayingTranslatedAudio(true)

            audio.onended = () => {
              console.log("🎵 HTML5 Audio playback ended")
              setIsPlayingTranslatedAudio(false)
              currentHtmlAudioRef.current = null
              URL.revokeObjectURL(audioUrl)
              setTranslationStatus("Translation active - listening for audio")
              resolve()
            }

            audio.onerror = (error) => {
              console.error("❌ HTML5 Audio error:", error)
              setIsPlayingTranslatedAudio(false)
              currentHtmlAudioRef.current = null
              URL.revokeObjectURL(audioUrl)
              setTranslationStatus("Audio playback error")
              resolve()
            }

            await audio.play()
            console.log("✅ HTML5 Audio playback started successfully")
          } catch (htmlAudioError) {
            console.error("❌ HTML5 Audio also failed:", htmlAudioError.message)
            setIsPlayingTranslatedAudio(false)
            setTranslationStatus(`Audio playback error: ${htmlAudioError.message}`)
            resolve()
          }
        }
      } catch (error) {
        console.error("🔊 Error playing queued audio:", error)
        setIsPlayingTranslatedAudio(false)
        setTranslationStatus(`Audio playback error: ${error.message}`)
        resolve()
      }
    })
  }

  // Chat functions using slice
  const handleSendChatMessage = async () => {
    if (!newMessage.trim() || !socketConnected || !currentStream) return

    const messageData = {
      session_id: currentStream.session_id,
      message: newMessage.trim(),
      sender_id: sessionStorage.getItem("userId"),
      sender_name: sessionStorage.getItem("name") || "Viewer",
    }

    console.log("📤 VIEWER SENDING MESSAGE WITH SESSION ID:", currentStream.session_id)
    console.log("📤 VIEWER MESSAGE DATA:", messageData)

    try {
      await sendChatMessage(messageData).unwrap()
      console.log("✅ VIEWER: Message sent successfully")
      setNewMessage("")
      // Immediately load chat history to see the new message
      setTimeout(loadChatHistory, 500)
    } catch (error) {
      console.error("❌ VIEWER: Error sending message:", error.message)
    }
  }

  const handleKeyDown = (e) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault()
      handleSendChatMessage()
    }
  }

  const toggleChat = () => {
    setIsChatOpen(!isChatOpen)
    if (!isChatOpen) {
      setUnreadMessages(0)
    }
  }

  const formatMessageTime = (timestamp) => {
    return new Date(timestamp).toLocaleTimeString([], {
      hour: "2-digit",
      minute: "2-digit",
    })
  }

  const getRoleColor = (role) => {
    switch (role) {
      case "kota_teacher":
      case "faculty":
        return "text-emerald-600"
      case "student":
        return "text-blue-600"
      case "center_counselor":
        return "text-purple-600"
      default:
        return "text-gray-600"
    }
  }

  const getRoleBadge = (role) => {
    switch (role) {
      case "kota_teacher":
      case "faculty":
        return "Teacher"
      case "student":
        return "Student"
      case "center_counselor":
        return "Counselor"
      default:
        return "User"
    }
  }

  // Test Socket.IO connection function
  const testSocketConnection = async () => {
    console.log("🧪 Testing Socket.IO connection...")
    const testUrls = ["https://sasthra.in"]

    for (const url of testUrls) {
      try {
        console.log(`🔍 Testing: ${url}`)
        const testSocket = io(url, {
          transports: ["polling"], // Start with polling only
          timeout: 5000,
          forceNew: true,
        })

        await new Promise((resolve, reject) => {
          const timeout = setTimeout(() => {
            testSocket.disconnect()
            reject(new Error("Timeout"))
          }, 5000)

          testSocket.on("connect", () => {
            clearTimeout(timeout)
            console.log(`✅ Test connection successful: ${url}`)
            testSocket.disconnect()
            resolve(url)
          })

          testSocket.on("connect_error", (error) => {
            clearTimeout(timeout)
            console.log(`❌ Test connection failed: ${url} - ${error.message}`)
            testSocket.disconnect()
            reject(error)
          })
        })

        // If we get here, connection was successful
        return url
      } catch (error) {
        console.log(`❌ Test failed for ${url}:`, error.message)
        continue
      }
    }

    throw new Error("All test connections failed")
  }

  // Add a test message function
  const sendTestMessage = () => {
    if (socket && socketConnected && currentStream) {
      const testMessage = {
        session_id: currentStream.session_id,
        message: "Test message from viewer",
        sender_id: sessionStorage.getItem("userId"),
        sender_name: sessionStorage.getItem("name") || "Viewer",
      }

      console.log("🧪 Sending test message:", testMessage)
      socket.emit("chat_message", testMessage)
    } else {
      console.log("❌ Cannot send test message - socket not connected or no stream")
    }
  }

  // Health check function
  const checkBackendHealth = async () => {
    const healthUrls = [
      "https://sasthra.in:8012/health",
      "https://sasthra.in/health",
      "http://sasthra.in:8012/health",
      "http://localhost:8012/health",
    ]

    console.log("🏥 Checking backend health...")

    for (const url of healthUrls) {
      try {
        console.log(`🔍 Checking: ${url}`)
        const response = await fetch(url, {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
        })

        if (response.ok) {
          const data = await response.json()
          console.log(`✅ Backend healthy at: ${url}`, data)
          return { url, data }
        }
      } catch (error) {
        console.log(`❌ Health check failed for ${url}:`, error.message)
        continue
      }
    }

    throw new Error("Backend health check failed for all URLs")
  }

  const formatUptime = (uptime) => {
    const hours = Math.floor(uptime / 3600)
    const minutes = Math.floor((uptime % 3600) / 60)
    const seconds = Math.floor(uptime % 60)
    if (hours > 0) return `${hours}h ${minutes}m ${seconds}s`
    if (minutes > 0) return `${minutes}m ${seconds}s`
    return `${seconds}s`
  }

  const formatDateTime = (dateString) => new Date(dateString).toLocaleString()

  if (isLoading)
    return (
      <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
        <div className="text-center">
          <div className="relative">
            <div className="w-20 h-20 border-4 border-purple-200 border-t-purple-600 rounded-full animate-spin mx-auto"></div>
            <div
              className="absolute inset-0 w-20 h-20 border-4 border-transparent border-r-pink-500 rounded-full animate-spin mx-auto"
              style={{ animationDirection: "reverse", animationDuration: "1.5s" }}
            ></div>
          </div>
          <p className="text-white text-lg font-medium mt-6 animate-pulse">Loading Live Streams...</p>
          <div className="flex justify-center mt-4 space-x-1">
            <div className="w-2 h-2 bg-purple-400 rounded-full animate-bounce"></div>
            <div className="w-2 h-2 bg-pink-400 rounded-full animate-bounce" style={{ animationDelay: "0.1s" }}></div>
            <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce" style={{ animationDelay: "0.2s" }}></div>
          </div>
        </div>
      </div>
    )

  if (error)
    return (
      <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-red-50 to-red-100">
        <div className="bg-white border border-red-200 rounded-2xl shadow-2xl p-8 max-w-md mx-4 transform hover:scale-105 transition-transform duration-300">
          <div className="flex items-center justify-center w-16 h-16 bg-red-100 rounded-full mx-auto mb-4">
            <svg className="h-8 w-8 text-red-600" viewBox="0 0 20 20" fill="currentColor">
              <path
                fillRule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                clipRule="evenodd"
              />
            </svg>
          </div>
          <h3 className="text-xl font-bold text-red-800 text-center mb-2">Connection Error</h3>
          <p className="text-red-700 text-center mb-4">{error.message || "Failed to load active streams"}</p>
          <button
            onClick={() => trigger()}
            className="w-full bg-red-600 hover:bg-red-700 text-white font-semibold py-3 px-4 rounded-lg transition-colors duration-200"
          >
            Try Again
          </button>
        </div>
      </div>
    )

  const activeStreams = data?.active_streams || []

  if (isViewingStream && currentStream)
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-slate-900 to-black">
        {/* Enhanced Header */}
        <div className="bg-gradient-to-r from-gray-800 via-gray-900 to-black text-white p-4 shadow-2xl border-b border-gray-700">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={handleLeaveStream}
                className="group bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white px-6 py-3 rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-red-500/25 flex items-center space-x-2"
              >
                <svg
                  className="w-5 h-5 group-hover:-translate-x-1 transition-transform duration-300"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                <span className="font-semibold">Leave Stream</span>
              </button>
              <div className="border-l border-gray-600 pl-4">
                <h1 className="text-2xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                  Live Stream Viewer
                </h1>
                <p className="text-sm text-gray-300 flex items-center">
                  <span className="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></span>
                  Session: {currentStream.session_id}
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-6">
              <div className="flex items-center bg-gray-800 rounded-lg px-4 py-2">
                <div
                  className={`w-3 h-3 rounded-full mr-3 ${livekitConnected ? "bg-green-400 animate-pulse" : "bg-red-400"}`}
                ></div>
                <span className="text-sm font-medium">{connectionStatus || "Connecting..."}</span>
              </div>
              <div className="flex items-center bg-gray-800 rounded-lg px-4 py-2">
                <svg className="w-4 h-4 mr-2 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"
                  />
                </svg>
                <span className="text-sm font-medium">{participants.length + 1} Participants</span>
              </div>
            </div>
          </div>
        </div>

        <div className="flex h-[calc(100vh-80px)]">
          {/* Enhanced Video Area */}
          <div className="flex-1 relative bg-black">
            <div className="w-full h-full flex items-center justify-center relative overflow-hidden">
              {/* Main Video Display */}
              <video
                ref={screenVideoRef}
                autoPlay
                playsInline
                className="w-full h-full object-contain transition-all duration-500 ease-in-out"
                style={{ maxHeight: "100%", maxWidth: "100%" }}
              />

              {!hasScreenShare && hasCameraTrack && (
                <video
                  ref={cameraMainRef}
                  autoPlay
                  playsInline
                  className="w-full h-full object-contain absolute inset-0 transition-all duration-500 ease-in-out"
                  style={{ maxHeight: "100%", maxWidth: "100%" }}
                />
              )}

              {/* Loading State */}
              {remoteVideoTracks.length === 0 && (
                <div className="absolute inset-0 flex items-center justify-center text-white bg-gradient-to-br from-gray-900/50 to-black/50 backdrop-blur-sm">
                  <div className="text-center">
                    <div className="relative mb-8">
                      <div className="w-20 h-20 border-4 border-blue-200 border-t-blue-500 rounded-full animate-spin mx-auto"></div>
                      <div
                        className="absolute inset-0 w-20 h-20 border-4 border-transparent border-r-purple-500 rounded-full animate-spin mx-auto"
                        style={{ animationDirection: "reverse", animationDuration: "1.5s" }}
                      ></div>
                    </div>
                    <p className="text-2xl font-bold mb-2 bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                      Waiting for stream...
                    </p>
                    <p className="text-gray-300 flex items-center justify-center">
                      <span
                        className={`w-2 h-2 rounded-full mr-2 ${livekitConnected ? "bg-green-400 animate-pulse" : "bg-red-400"}`}
                      ></span>
                      {livekitConnected ? "Connected to room" : "Connecting..."}
                    </p>
                  </div>
                </div>
              )}
            </div>

            {/* Picture-in-Picture Camera */}
            {hasScreenShare && hasCameraTrack && (
              <div className="absolute bottom-6 right-6 w-72 h-52 bg-gray-900 rounded-2xl overflow-hidden border-2 border-gray-600 shadow-2xl transform hover:scale-105 transition-all duration-300">
                <video ref={cameraVideoRef} autoPlay playsInline muted className="w-full h-full object-cover" />
                <div className="absolute bottom-3 left-3 bg-black/70 backdrop-blur-sm text-white text-xs px-3 py-1 rounded-full flex items-center">
                  <div
                    className={`w-2 h-2 rounded-full mr-2 ${hasCameraTrack ? "bg-green-400 animate-pulse" : "bg-red-400"}`}
                  ></div>
                  Teacher Camera
                </div>
              </div>
            )}

            {/* Floating Chat Button */}
            {!isChatOpen && (
              <button
                onClick={toggleChat}
                className="absolute bottom-6 left-6 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white p-4 rounded-full shadow-2xl transition-all duration-300 transform hover:scale-110 hover:shadow-blue-500/25 flex items-center space-x-2 group"
              >
                <svg
                  className="w-6 h-6 group-hover:rotate-12 transition-transform duration-300"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.959 8.959 0 01-4.906-1.405L3 21l2.595-5.094A8.959 8.959 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z"
                  />
                </svg>
                {unreadMessages > 0 && (
                  <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs px-2 py-1 rounded-full animate-bounce shadow-lg">
                    {unreadMessages}
                  </span>
                )}
              </button>
            )}
          </div>

          {/* Enhanced Sidebar */}
          <div className="w-96 bg-gradient-to-b from-gray-900 via-gray-800 to-gray-900 text-white overflow-y-auto shadow-2xl border-l border-gray-700">
            <div className="p-6 space-y-8">
              {/* Stream Information Card */}
              <div className="bg-gradient-to-br from-gray-800 to-gray-900 rounded-2xl p-6 border border-gray-700 shadow-xl">
                <h3 className="text-xl font-bold mb-4 flex items-center">
                  <svg className="w-6 h-6 mr-2 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                  Stream Information
                </h3>
                <div className="space-y-3">
                  <div className="flex justify-between items-center p-3 bg-gray-700/50 rounded-lg">
                    <span className="text-gray-300">Teacher ID:</span>
                    <span className="font-semibold text-blue-400">{currentStream.teacher_id}</span>
                  </div>
                  <div className="flex justify-between items-center p-3 bg-gray-700/50 rounded-lg">
                    <span className="text-gray-300">Quality:</span>
                    <span className="font-semibold text-green-400">{currentStream.quality || "Standard"}</span>
                  </div>
                  <div className="flex justify-between items-center p-3 bg-gray-700/50 rounded-lg">
                    <span className="text-gray-300">Viewers:</span>
                    <span className="font-semibold text-purple-400">{currentStream.viewer_count || 0}</span>
                  </div>
                </div>
              </div>

              {/* Features Card */}
              <div className="bg-gradient-to-br from-gray-800 to-gray-900 rounded-2xl p-6 border border-gray-700 shadow-xl">
                <h3 className="text-xl font-bold mb-4 flex items-center">
                  <svg className="w-6 h-6 mr-2 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                  Features
                </h3>
                <div className="space-y-3">
                  {currentStream.features?.screen_sharing && (
                    <div className="flex items-center p-3 bg-green-500/10 border border-green-500/20 rounded-lg">
                      <div className="w-3 h-3 bg-green-400 rounded-full mr-3 animate-pulse"></div>
                      <span className="text-green-400 font-medium">Screen Sharing Active</span>
                    </div>
                  )}
                  {currentStream.features?.chat_enabled && (
                    <div className="flex items-center p-3 bg-blue-500/10 border border-blue-500/20 rounded-lg">
                      <div className="w-3 h-3 bg-blue-400 rounded-full mr-3 animate-pulse"></div>
                      <span className="text-blue-400 font-medium">Chat Available</span>
                    </div>
                  )}
                  {currentStream.features?.recording_enabled && (
                    <div className="flex items-center p-3 bg-red-500/10 border border-red-500/20 rounded-lg">
                      <div className="w-3 h-3 bg-red-400 rounded-full mr-3 animate-pulse"></div>
                      <span className="text-red-400 font-medium">Recording Active</span>
                    </div>
                  )}
                </div>
              </div>

              {/* Participants Card */}
              <div className="bg-gradient-to-br from-gray-800 to-gray-900 rounded-2xl p-6 border border-gray-700 shadow-xl">
                <h3 className="text-xl font-bold mb-4 flex items-center">
                  <svg className="w-6 h-6 mr-2 text-purple-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"
                    />
                  </svg>
                  Participants ({participants.length + 1})
                </h3>
                <div className="space-y-3 max-h-40 overflow-y-auto">
                  <div className="flex items-center p-3 bg-gradient-to-r from-green-500/10 to-emerald-500/10 border border-green-500/20 rounded-lg">
                    <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center text-sm font-bold mr-3">
                      T
                    </div>
                    <div className="flex-1">
                      <div className="font-semibold text-green-400">Teacher</div>
                      <div className="text-xs text-gray-400">Host</div>
                    </div>
                    <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  </div>
                  {participants.map((participant, index) => (
                    <div
                      key={participant.identity || index}
                      className="flex items-center p-3 bg-gradient-to-r from-blue-500/10 to-purple-500/10 border border-blue-500/20 rounded-lg"
                    >
                      <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-sm font-bold mr-3">
                        {participant.identity?.charAt(0)?.toUpperCase() || "U"}
                      </div>
                      <div className="flex-1">
                        <div className="font-semibold text-blue-400">{participant.identity || "Unknown"}</div>
                        <div className="text-xs text-gray-400">Viewer</div>
                      </div>
                      <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Connection Status Card */}
              <div className="bg-gradient-to-br from-gray-800 to-gray-900 rounded-2xl p-6 border border-gray-700 shadow-xl">
                <h3 className="text-xl font-bold mb-4 flex items-center">
                  <svg className="w-6 h-6 mr-2 text-yellow-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M8.111 16.404a5.5 5.5 0 017.778 0M12 20h.01m-7.08-7.071c3.904-3.905 10.236-3.905 14.141 0M1.394 9.393c5.857-5.857 15.355-5.857 21.213 0"
                    />
                  </svg>
                  Connection Status
                </h3>
                <div className="space-y-3">
                  <div
                    className={`flex items-center p-3 rounded-lg ${livekitConnected ? "bg-green-500/10 border border-green-500/20" : "bg-red-500/10 border border-red-500/20"}`}
                  >
                    <div
                      className={`w-3 h-3 rounded-full mr-3 ${livekitConnected ? "bg-green-400 animate-pulse" : "bg-red-400"}`}
                    ></div>
                    <span className={`font-medium ${livekitConnected ? "text-green-400" : "text-red-400"}`}>
                      {livekitConnected ? "Connected" : "Disconnected"}
                    </span>
                  </div>
                  <div className="grid grid-cols-2 gap-3 text-sm">
                    <div className="bg-gray-700/50 p-3 rounded-lg text-center">
                      <div className="text-gray-400">Video Tracks</div>
                      <div className="text-xl font-bold text-blue-400">{remoteVideoTracks.length}</div>
                    </div>
                    <div className="bg-gray-700/50 p-3 rounded-lg text-center">
                      <div className="text-gray-400">Audio Tracks</div>
                      <div className="text-xl font-bold text-green-400">{remoteAudioTracks.length}</div>
                    </div>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-400">Screen Share:</span>
                    <span className={hasScreenShare ? "text-green-400" : "text-gray-500"}>
                      {hasScreenShare ? "Active ✅" : "Inactive ❌"}
                    </span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-400">Camera:</span>
                    <span className={hasCameraTrack ? "text-green-400" : "text-gray-500"}>
                      {hasCameraTrack ? "Active ✅" : "Inactive ❌"}
                    </span>
                  </div>
                </div>
              </div>

              {/* Enhanced Translation Card */}
              <div className="bg-gradient-to-br from-indigo-900/50 to-purple-900/50 rounded-2xl p-6 border border-indigo-500/20 shadow-xl">
                <h3 className="text-xl font-bold mb-4 flex items-center">
                  <svg className="w-6 h-6 mr-2 text-indigo-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129"
                    />
                  </svg>
                  Live Translation
                </h3>

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-gray-300 font-medium">Enable Translation</span>
                    <button
                      onClick={isTranslationEnabled ? stopTranslation : startTranslation}
                      disabled={!remoteAudioTracks.length}
                      className={`px-4 py-2 rounded-xl font-semibold transition-all duration-300 transform hover:scale-105 ${
                        isTranslationEnabled
                          ? "bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white shadow-lg hover:shadow-red-500/25"
                          : "bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white disabled:from-gray-600 disabled:to-gray-700 disabled:cursor-not-allowed shadow-lg hover:shadow-green-500/25"
                      }`}
                    >
                      {isTranslationEnabled ? "Stop" : "Start"}
                    </button>
                  </div>

                  <div className="grid grid-cols-2 gap-3">
                    <div>
                      <label className="block text-xs text-gray-400 mb-2 font-medium">From Language</label>
                      <select
                        value={sourceLanguage}
                        onChange={(e) => setSourceLanguage(e.target.value)}
                        disabled={isTranslationEnabled}
                        className="w-full bg-gray-800 border border-gray-600 rounded-lg px-3 py-2 text-sm text-white disabled:opacity-50 focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200"
                      >
                        {availableLanguages.map((lang) => (
                          <option key={lang.code} value={lang.code}>
                            {lang.flag} {lang.name}
                          </option>
                        ))}
                      </select>
                    </div>
                    <div>
                      <label className="block text-xs text-gray-400 mb-2 font-medium">To Language</label>
                      <select
                        value={targetLanguage}
                        onChange={(e) => setTargetLanguage(e.target.value)}
                        disabled={isTranslationEnabled}
                        className="w-full bg-gray-800 border border-gray-600 rounded-lg px-3 py-2 text-sm text-white disabled:opacity-50 focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200"
                      >
                        {availableLanguages.map((lang) => (
                          <option key={lang.code} value={lang.code}>
                            {lang.flag} {lang.name}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>

                  {/* Translation Status */}
                  <div className="space-y-2">
                    <div
                      className={`flex items-center p-3 rounded-lg ${isTranslationEnabled ? "bg-green-500/10 border border-green-500/20" : "bg-gray-700/50"}`}
                    >
                      <div
                        className={`w-3 h-3 rounded-full mr-3 ${isTranslationEnabled ? "bg-green-400 animate-pulse" : "bg-gray-400"}`}
                      ></div>
                      <span className="text-sm font-medium">
                        {translationStatus || (isTranslationEnabled ? "Translation Active" : "Translation Inactive")}
                      </span>
                    </div>

                    {isPlayingTranslatedAudio && (
                      <div className="flex items-center p-3 bg-blue-500/10 border border-blue-500/20 rounded-lg">
                        <div className="w-3 h-3 bg-blue-400 rounded-full mr-3 animate-pulse"></div>
                        <span className="text-sm font-medium text-blue-400">Playing Translated Audio</span>
                      </div>
                    )}

                    {audioQueueLength > 0 && (
                      <div className="flex items-center p-3 bg-yellow-500/10 border border-yellow-500/20 rounded-lg">
                        <div className="w-3 h-3 bg-yellow-400 rounded-full mr-3 animate-pulse"></div>
                        <span className="text-sm font-medium text-yellow-400">
                          Audio Queue: {audioQueueLength} pending
                        </span>
                      </div>
                    )}
                  </div>

                  {/* Translation Details */}
                  {isTranslationEnabled && (
                    <div className="bg-gray-800/50 rounded-lg p-3 space-y-2 text-xs">
                      <div className="flex justify-between">
                        <span className="text-gray-400">WebSocket:</span>
                        <span
                          className={
                            translationWebSocket
                              ? translationWebSocket.readyState === 1
                                ? "text-green-400"
                                : translationWebSocket.readyState === 0
                                  ? "text-yellow-400"
                                  : translationWebSocket.readyState === 2
                                    ? "text-orange-400"
                                    : "text-red-400"
                              : "text-red-400"
                          }
                        >
                          {translationWebSocket
                            ? translationWebSocket.readyState === 1
                              ? "🟢 Connected"
                              : translationWebSocket.readyState === 0
                                ? "🟡 Connecting"
                                : translationWebSocket.readyState === 2
                                  ? "🟠 Closing"
                                  : "🔴 Closed"
                            : "❌ Not Created"}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Session:</span>
                        <span className="text-gray-300">{translationSession || "None"}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Queue Status:</span>
                        <span className="text-gray-300">
                          {audioQueueLength === 0 ? "Empty" : `${audioQueueLength} items`}
                        </span>
                      </div>
                    </div>
                  )}

                  {/* Translation Display */}
                  {currentTranscription && (
                    <div className="bg-gray-800/50 rounded-lg p-4 border-l-4 border-blue-500">
                      <div className="text-xs text-blue-400 mb-2 font-semibold">
                        Original ({sourceLanguage.toUpperCase()}):
                      </div>
                      <div className="text-sm text-white">{currentTranscription}</div>
                    </div>
                  )}

                  {currentTranslation && (
                    <div className="bg-indigo-900/30 rounded-lg p-4 border-l-4 border-indigo-400">
                      <div className="text-xs text-indigo-400 mb-2 font-semibold">
                        Translation ({targetLanguage.toUpperCase()}):
                      </div>
                      <div className="text-sm text-white">{currentTranslation}</div>
                    </div>
                  )}

                  {!remoteAudioTracks.length && (
                    <div className="flex items-center p-3 bg-yellow-500/10 border border-yellow-500/20 rounded-lg">
                      <svg
                        className="w-4 h-4 text-yellow-400 mr-2"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                        />
                      </svg>
                      <span className="text-xs text-yellow-400 font-medium">Audio track required for translation</span>
                    </div>
                  )}
                </div>
              </div>

              {/* Enhanced Chat Section */}
              <div className="bg-gradient-to-br from-blue-900/50 to-purple-900/50 rounded-2xl border border-blue-500/20 shadow-xl">
                <div className="flex items-center justify-between p-6 border-b border-blue-500/20">
                  <h3 className="text-xl font-bold flex items-center">
                    <svg className="w-6 h-6 mr-2 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.959 8.959 0 01-4.906-1.405L3 21l2.595-5.094A8.959 8.959 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z"
                      />
                    </svg>
                    Chat
                    {unreadMessages > 0 && !isChatOpen && (
                      <span className="bg-red-500 text-white text-xs px-2 py-1 rounded-full ml-2 animate-bounce">
                        {unreadMessages}
                      </span>
                    )}
                  </h3>
                  <button
                    onClick={toggleChat}
                    className="text-sm text-blue-400 hover:text-blue-300 font-medium transition-colors duration-200"
                  >
                    {isChatOpen ? "Hide" : "Show"}
                  </button>
                </div>

                {/* Session ID Debug Display */}
                {currentStream && (
                  <div className="p-4 bg-blue-900/30 border-b border-blue-500/20">
                    <p className="text-xs font-bold text-blue-200 mb-1">🔍 VIEWER CHAT SESSION ID:</p>
                    <p className="text-xs text-blue-300 break-all font-mono bg-blue-800/30 p-2 rounded">
                      {currentStream.session_id}
                    </p>
                    <div className="mt-2">
                      <input
                        type="text"
                        placeholder="Override session ID for testing"
                        className="w-full text-xs p-2 bg-blue-800/50 text-blue-100 border border-blue-600/50 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                        onKeyDown={(e) => {
                          if (e.key === "Enter" && e.target.value.trim()) {
                            const newSessionId = e.target.value.trim()
                            console.log(`🔧 VIEWER: Overriding session ID to: ${newSessionId}`)
                            setCurrentStream((prev) => ({
                              ...prev,
                              session_id: newSessionId,
                            }))
                            e.target.value = ""
                          }
                        }}
                      />
                      <p className="text-xs text-blue-400 mt-1">Press Enter to override session ID</p>
                    </div>
                  </div>
                )}

                {isChatOpen && (
                  <div className="p-6 space-y-4">
                    {/* Chat Messages */}
                    <div className="bg-gray-800/50 rounded-xl border border-gray-600/50 h-80 overflow-y-auto p-4 space-y-3 scrollbar-thin scrollbar-thumb-gray-600 scrollbar-track-gray-800">
                      {isLoadingHistory ? (
                        <div className="flex items-center justify-center h-full">
                          <div className="text-center">
                            <div className="w-8 h-8 border-2 border-blue-200 border-t-blue-500 rounded-full animate-spin mx-auto mb-2"></div>
                            <span className="text-sm text-gray-400">Loading messages...</span>
                          </div>
                        </div>
                      ) : chatMessages.length === 0 ? (
                        <div className="flex items-center justify-center h-full">
                          <div className="text-center">
                            <svg
                              className="w-12 h-12 text-gray-500 mx-auto mb-2"
                              fill="none"
                              viewBox="0 0 24 24"
                              stroke="currentColor"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={1}
                                d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.959 8.959 0 01-4.906-1.405L3 21l2.595-5.094A8.959 8.959 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z"
                              />
                            </svg>
                            <p className="text-sm text-gray-400">No messages yet</p>
                            <p className="text-xs text-gray-500 mt-1">Start the conversation!</p>
                          </div>
                        </div>
                      ) : (
                        chatMessages.map((message, index) => (
                          <div
                            key={`${message.id || "msg"}-${index}-${message.timestamp || Date.now()}`}
                            className="group hover:bg-gray-700/30 rounded-lg p-3 transition-all duration-200"
                          >
                            <div className="flex items-start justify-between">
                              <div className="flex-1">
                                <div className="flex items-center space-x-2 mb-1">
                                  <span className={`text-sm font-semibold ${getRoleColor(message.sender_role)}`}>
                                    {message.sender_name}
                                  </span>
                                  <span className="text-xs bg-gray-700 px-2 py-1 rounded-full">
                                    {getRoleBadge(message.sender_role)}
                                  </span>
                                </div>
                                <p className="text-sm text-gray-200 leading-relaxed">{message.message}</p>
                              </div>
                              <span className="text-xs text-gray-400 ml-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                                {formatMessageTime(message.timestamp)}
                              </span>
                            </div>
                          </div>
                        ))
                      )}
                    </div>

                    {/* Chat Input */}
                    <div className="flex space-x-3">
                      <input
                        type="text"
                        value={newMessage}
                        onChange={(e) => setNewMessage(e.target.value)}
                        onKeyDown={handleKeyDown}
                        placeholder="Ask a question..."
                        className="flex-1 bg-gray-800/50 border border-gray-600/50 rounded-xl px-4 py-3 text-sm text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                        disabled={!socketConnected}
                      />
                      <button
                        onClick={handleSendChatMessage}
                        disabled={!newMessage.trim() || !socketConnected || isSendingMessage}
                        className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-6 py-3 rounded-xl font-semibold hover:shadow-lg hover:shadow-blue-500/25 disabled:from-gray-600 disabled:to-gray-700 disabled:cursor-not-allowed transition-all duration-300 transform hover:scale-105 disabled:transform-none"
                      >
                        {isSendingMessage ? (
                          <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                        ) : (
                          <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"
                            />
                          </svg>
                        )}
                      </button>
                    </div>

                    {/* Socket Status */}
                    <div className="flex items-center justify-between text-xs bg-gray-800/30 rounded-lg p-3">
                      <div className="flex items-center">
                        <span
                          className={`w-2 h-2 rounded-full mr-2 ${socketConnected ? "bg-green-400 animate-pulse" : "bg-red-400"}`}
                        ></span>
                        <span className="text-gray-400">Chat {socketConnected ? "Connected" : "Disconnected"}</span>
                      </div>
                      <div className="flex space-x-2">
                        <button
                          onClick={async () => {
                            try {
                              const result = await checkBackendHealth()
                              alert(`✅ Backend is healthy!\nURL: ${result.url}\nService: ${result.data.service}`)
                            } catch (error) {
                              alert(`❌ Backend health check failed: ${error.message}`)
                            }
                          }}
                          className="bg-blue-600 hover:bg-blue-700 text-white px-2 py-1 rounded text-xs transition-colors duration-200"
                          title="Check Backend Health"
                        >
                          🏥 Health
                        </button>
                        <button
                          onClick={testSocketConnection}
                          className="bg-yellow-600 hover:bg-yellow-700 text-white px-2 py-1 rounded text-xs transition-colors duration-200"
                          title="Test Socket Connection"
                        >
                          🧪 Test
                        </button>
                        {socketConnected && (
                          <button
                            onClick={sendTestMessage}
                            className="bg-green-600 hover:bg-green-700 text-white px-2 py-1 rounded text-xs transition-colors duration-200"
                            title="Send Test Message"
                          >
                            📤 Test Msg
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    )

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      <div className="max-w-7xl mx-auto px-6 py-8">
        {/* Enhanced Header */}
        <div className="mb-12 text-center">
          <h1 className="text-5xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent mb-4">
            Live Streams
          </h1>
          <p className="text-xl text-gray-600 mb-6">Join active streaming sessions from teachers</p>

          <div className="flex items-center justify-center space-x-8 bg-white rounded-2xl shadow-lg p-6 max-w-2xl mx-auto">
            <div className="flex items-center">
              <div className="w-4 h-4 bg-green-500 rounded-full mr-3 animate-pulse"></div>
              <span className="text-lg font-semibold text-gray-700">
                {activeStreams.length} Active Stream{activeStreams.length !== 1 ? "s" : ""}
              </span>
            </div>
            <button
              onClick={() => trigger()}
              className="group bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-6 py-3 rounded-xl font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-blue-500/25 flex items-center space-x-2"
            >
              <svg
                className="w-5 h-5 group-hover:rotate-180 transition-transform duration-500"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                />
              </svg>
              <span>Refresh</span>
            </button>
          </div>
        </div>

        {activeStreams.length === 0 ? (
          <div className="text-center py-20">
            <div className="bg-white rounded-3xl shadow-2xl p-12 max-w-md mx-auto transform hover:scale-105 transition-transform duration-300">
              <div className="mx-auto h-32 w-32 text-gray-300 mb-8">
                <svg fill="none" viewBox="0 0 24 24" stroke="currentColor" className="w-full h-full">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={1}
                    d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
                  />
                </svg>
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">No Active Streams</h3>
              <p className="text-gray-500 text-lg">There are currently no live streaming sessions available.</p>
              <div className="mt-6">
                <button
                  onClick={() => trigger()}
                  className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-3 rounded-xl font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-blue-500/25"
                >
                  Check Again
                </button>
              </div>
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {activeStreams.map((stream, index) => (
              <div
                key={stream.session_id || index}
                className="group bg-white rounded-3xl shadow-xl border border-gray-100 overflow-hidden hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 hover:scale-105"
              >
                {/* Stream Header */}
                <div className="bg-gradient-to-r from-red-500 via-pink-500 to-purple-600 p-6 relative overflow-hidden">
                  <div className="absolute inset-0 bg-black/10"></div>
                  <div className="relative z-10">
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center">
                        <div className="w-4 h-4 bg-red-400 rounded-full mr-3 animate-pulse shadow-lg"></div>
                        <span className="text-white font-bold text-lg tracking-wide">LIVE</span>
                      </div>
                      <div className="bg-white/20 backdrop-blur-sm text-white text-sm px-3 py-1 rounded-full font-medium">
                        {stream.quality || "Standard"} Quality
                      </div>
                    </div>
                    <div className="absolute top-4 right-4 opacity-20">
                      <svg className="w-16 h-16 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={1}
                          d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
                        />
                      </svg>
                    </div>
                  </div>
                </div>

                {/* Stream Content */}
                <div className="p-8">
                  <div className="space-y-6">
                    <div>
                      <h3 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors duration-300">
                        Session: {stream.session_id}
                      </h3>
                      <p className="text-gray-600 flex items-center">
                        <svg
                          className="w-4 h-4 mr-2 text-blue-500"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                          />
                        </svg>
                        Teacher ID: {stream.teacher_id}
                      </p>
                    </div>

                    {/* Stats Grid */}
                    <div className="grid grid-cols-2 gap-4">
                      <div className="text-center p-4 bg-gradient-to-br from-blue-50 to-indigo-100 rounded-2xl border border-blue-200 group-hover:from-blue-100 group-hover:to-indigo-200 transition-all duration-300">
                        <div className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                          {stream.viewer_count || 0}
                        </div>
                        <div className="text-sm text-gray-600 font-medium">Viewers</div>
                      </div>
                      <div className="text-center p-4 bg-gradient-to-br from-green-50 to-emerald-100 rounded-2xl border border-green-200 group-hover:from-green-100 group-hover:to-emerald-200 transition-all duration-300">
                        <div className="text-3xl font-bold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
                          {formatUptime(stream.uptime || 0)}
                        </div>
                        <div className="text-sm text-gray-600 font-medium">Uptime</div>
                      </div>
                    </div>

                    {/* Features */}
                    <div>
                      <h4 className="text-sm font-bold text-gray-900 mb-3 flex items-center">
                        <svg
                          className="w-4 h-4 mr-2 text-purple-500"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                          />
                        </svg>
                        Features
                      </h4>
                      <div className="flex flex-wrap gap-2">
                        {stream.features?.screen_sharing && (
                          <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-blue-100 text-blue-800 border border-blue-200">
                            <svg className="w-3 h-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                              />
                            </svg>
                            Screen Sharing
                          </span>
                        )}
                        {stream.features?.chat_enabled && (
                          <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-green-100 text-green-800 border border-green-200">
                            <svg className="w-3 h-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.959 8.959 0 01-4.906-1.405L3 21l2.595-5.094A8.959 8.959 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z"
                              />
                            </svg>
                            Chat
                          </span>
                        )}
                        {stream.features?.recording_enabled && (
                          <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-red-100 text-red-800 border border-red-200">
                            <svg className="w-3 h-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
                              />
                            </svg>
                            Recording
                          </span>
                        )}
                      </div>
                    </div>

                    <div className="text-xs text-gray-500 flex items-center">
                      <svg className="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                        />
                      </svg>
                      Started: {formatDateTime(stream.created_at)}
                    </div>
                  </div>

                  {/* Join Button */}
                  <div className="mt-8">
                    <button
                      onClick={() => handleJoinStream(stream)}
                      disabled={isJoining}
                      className="group w-full bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 hover:from-blue-700 hover:via-purple-700 hover:to-indigo-700 disabled:from-gray-400 disabled:via-gray-500 disabled:to-gray-600 text-white font-bold py-4 px-6 rounded-2xl transition-all duration-500 transform hover:scale-105 hover:shadow-2xl hover:shadow-blue-500/25 disabled:transform-none disabled:shadow-none flex items-center justify-center relative overflow-hidden"
                    >
                      <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
                      {isJoining ? (
                        <div className="flex items-center relative z-10">
                          <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-3"></div>
                          <span className="text-lg">Joining Stream...</span>
                        </div>
                      ) : (
                        <div className="flex items-center relative z-10">
                          <svg
                            className="w-6 h-6 mr-3 group-hover:scale-110 transition-transform duration-300"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1.586a1 1 0 01.707.293l2.414 2.414a1 1 0 00.707.293H15M9 10V9a2 2 0 012-2h2a2 2 0 012 2v1M9 10v5a2 2 0 002 2h2a2 2 0 002-2v-5"
                            />
                          </svg>
                          <span className="text-lg">Join Stream</span>
                        </div>
                      )}
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Enhanced Footer Info */}
        {data && (
          <div className="mt-16 bg-white rounded-3xl shadow-xl border border-gray-100 p-8">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-6">
                <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center">
                  <svg className="w-8 h-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-gray-900">{data.service || "Streaming Service"}</h3>
                  <p className="text-gray-600 text-lg">
                    Total Streams Available:{" "}
                    <span className="font-semibold text-blue-600">{data.total_count || 0}</span>
                  </p>
                </div>
              </div>
              <div className="text-right">
                <div className="text-sm text-gray-500 mb-1">Last Updated</div>
                <div className="text-lg font-semibold text-gray-900">{formatDateTime(data.timestamp)}</div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default CenterTraineeLiveViewer
// import { useEffect, useState, useRef } from "react"
// import { Room, RoomEvent } from "livekit-client"
// import io from "socket.io-client"
// import {
//   useLazyGetCenterLiveViewerQuery,
//   useJoinLiveStreamMutation,
//   useStartTranslationSessionMutation,
//   useStopTranslationSessionMutation,
//   useSendChatMessageMutation,
//   useLazyGetChatHistoryQuery,
// } from "./centerTraineeLive.slice"

// const CenterTraineeLiveViewer = () => {
//   const [trigger, { data, error, isLoading }] = useLazyGetCenterLiveViewerQuery()
//   const [joinStream, { isLoading: isJoining }] = useJoinLiveStreamMutation()
//   const [startTranslationSession] = useStartTranslationSessionMutation()
//   const [stopTranslationSession] = useStopTranslationSessionMutation()
//   const [sendChatMessage, { isLoading: isSendingMessage }] = useSendChatMessageMutation()
//   const [getChatHistory, { data: chatHistoryData, isLoading: isLoadingHistory }] = useLazyGetChatHistoryQuery()

//   // Stream viewing states
//   const [isViewingStream, setIsViewingStream] = useState(false)
//   const [currentStream, setCurrentStream] = useState(null)
//   const [livekitRoom, setLivekitRoom] = useState(null)
//   const [livekitConnected, setLivekitConnected] = useState(false)
//   const [participants, setParticipants] = useState([])
//   const [remoteVideoTracks, setRemoteVideoTracks] = useState([])
//   const [remoteAudioTracks, setRemoteAudioTracks] = useState([])
//   const [connectionStatus, setConnectionStatus] = useState("")
//   const [hasScreenShare, setHasScreenShare] = useState(false)
//   const [hasCameraTrack, setHasCameraTrack] = useState(false)

//   // Refs for video elements
//   const mainVideoRef = useRef(null)
//   const screenVideoRef = useRef(null)
//   const cameraVideoRef = useRef(null)
//   const cameraMainRef = useRef(null)

//   // Translation states
//   const [isTranslationEnabled, setIsTranslationEnabled] = useState(false)
//   const [translationSession, setTranslationSession] = useState(null)
//   const [translationWebSocket, setTranslationWebSocket] = useState(null)
//   const [sourceLanguage, setSourceLanguage] = useState("en")
//   const [targetLanguage, setTargetLanguage] = useState("ta")
//   const [translationStatus, setTranslationStatus] = useState("")
//   const [currentTranscription, setCurrentTranscription] = useState("")
//   const [currentTranslation, setCurrentTranslation] = useState("")
//   const [isPlayingTranslatedAudio, setIsPlayingTranslatedAudio] = useState(false)
//   const [audioQueueLength, setAudioQueueLength] = useState(0)

//   // Translation audio refs
//   const audioContextRef = useRef(null)
//   const mediaRecorderRef = useRef(null)
//   const translationWebSocketRef = useRef(null)

//   // Audio Queue System refs and state
//   const audioQueueRef = useRef([])
//   const currentAudioSourceRef = useRef(null)
//   const currentHtmlAudioRef = useRef(null)
//   const isProcessingQueueRef = useRef(false)

//   // Chat states
//   const [chatMessages, setChatMessages] = useState([])
//   const [newMessage, setNewMessage] = useState("")
//   const [isChatOpen, setIsChatOpen] = useState(false)
//   const [unreadMessages, setUnreadMessages] = useState(0)

//   // Socket.IO states
//   const [socket, setSocket] = useState(null)
//   const [socketConnected, setSocketConnected] = useState(false)

//   // Available languages
//   const availableLanguages = [
//     { code: "en", name: "English", flag: "🇺🇸" },
//     { code: "ta", name: "Tamil", flag: "🇮🇳" },
//     { code: "hi", name: "Hindi", flag: "🇮🇳" },
//     { code: "te", name: "Telugu", flag: "🇮🇳" },
//     { code: "kn", name: "Kannada", flag: "🇮🇳" },
//   ]

//   // Mute/unmute original audio based on translation state
//   useEffect(() => {
//     remoteAudioTracks.forEach(({ audioElement }) => {
//       if (audioElement) {
//         audioElement.muted = isTranslationEnabled
//       }
//     })
//   }, [isTranslationEnabled, remoteAudioTracks])

//   useEffect(() => {
//     trigger()
//     const interval = setInterval(() => trigger(), 30000)
//     return () => clearInterval(interval)
//   }, [trigger])

//   useEffect(() => {
//     return () => {
//       if (livekitRoom) {
//         livekitRoom.disconnect()
//         setLivekitRoom(null)
//       }
//       cleanupTranslation()
//     }
//   }, [livekitRoom])

//   // HTTP-based chat system (more reliable than Socket.IO)
//   useEffect(() => {
//     if (isViewingStream && currentStream) {
//       console.log("💬 VIEWER: Starting HTTP-based chat for session:", currentStream.session_id)
//       setSocketConnected(true) // Mark as "connected" for UI purposes

//       // Load initial chat history
//       loadChatHistory()

//       // Start polling for new messages every 2 seconds
//       const pollInterval = setInterval(() => {
//         loadChatHistory()
//       }, 2000)

//       return () => {
//         clearInterval(pollInterval)
//         setSocketConnected(false)
//       }
//     }
//   }, [isViewingStream, currentStream])

//   // Load chat history using slice
//   const loadChatHistory = async () => {
//     if (!currentStream?.session_id) return

//     try {
//       console.log(`🔍 VIEWER: Loading chat history for session: ${currentStream.session_id}`)
//       const response = await getChatHistory(currentStream.session_id).unwrap()
//       const newMessages = response.messages || []
//       console.log(`🔍 VIEWER: API returned ${newMessages.length} messages for session ${currentStream.session_id}`)

//       setChatMessages((prev) => {
//         // Only update if there are new messages
//         if (newMessages.length !== prev.length) {
//           console.log(`💬 VIEWER: Updated chat with ${newMessages.length} messages`)
//           // Check for new messages to increment unread count
//           const newCount = newMessages.length - prev.length
//           if (newCount > 0 && !isChatOpen) {
//             setUnreadMessages((prev) => prev + newCount)
//           }
//           return newMessages
//         }
//         return prev
//       })
//     } catch (error) {
//       console.log("❌ VIEWER: Failed to load chat history:", error.message)
//     }
//   }

//   // Audio Queue Management Functions
//   const addToAudioQueue = (audioData) => {
//     console.log("📥 Adding audio to queue, current queue length:", audioQueueRef.current.length)
//     audioQueueRef.current.push(audioData)
//     setAudioQueueLength(audioQueueRef.current.length)
//     processAudioQueue()
//   }

//   const processAudioQueue = async () => {
//     if (isProcessingQueueRef.current || audioQueueRef.current.length === 0) {
//       return
//     }

//     isProcessingQueueRef.current = true
//     console.log("🎵 Processing audio queue, items:", audioQueueRef.current.length)

//     while (audioQueueRef.current.length > 0) {
//       const audioData = audioQueueRef.current.shift()
//       setAudioQueueLength(audioQueueRef.current.length)
//       await playAudioFromQueue(audioData)
//     }

//     isProcessingQueueRef.current = false
//     setAudioQueueLength(0)
//     console.log("✅ Audio queue processing completed")
//   }

//   const stopCurrentAudio = () => {
//     // Stop Web Audio API source
//     if (currentAudioSourceRef.current) {
//       try {
//         currentAudioSourceRef.current.stop()
//         currentAudioSourceRef.current.disconnect()
//       } catch (error) {
//         console.warn("⚠️ Error stopping Web Audio source:", error)
//       }
//       currentAudioSourceRef.current = null
//     }

//     // Stop HTML5 Audio
//     if (currentHtmlAudioRef.current) {
//       try {
//         currentHtmlAudioRef.current.pause()
//         currentHtmlAudioRef.current.currentTime = 0
//         if (currentHtmlAudioRef.current.src) {
//           URL.revokeObjectURL(currentHtmlAudioRef.current.src)
//         }
//       } catch (error) {
//         console.warn("⚠️ Error stopping HTML5 Audio:", error)
//       }
//       currentHtmlAudioRef.current = null
//     }

//     setIsPlayingTranslatedAudio(false)
//   }

//   const clearAudioQueue = () => {
//     console.log("🗑️ Clearing audio queue")
//     audioQueueRef.current = []
//     setAudioQueueLength(0)
//     stopCurrentAudio()
//     isProcessingQueueRef.current = false
//   }

//   const cleanupTranslation = () => {
//     if (translationWebSocket) {
//       translationWebSocket.close()
//       setTranslationWebSocket(null)
//     }

//     if (translationWebSocketRef.current) {
//       translationWebSocketRef.current.close()
//       translationWebSocketRef.current = null
//     }

//     if (mediaRecorderRef.current) {
//       if (mediaRecorderRef.current.disconnect) {
//         mediaRecorderRef.current.disconnect()
//       } else if (mediaRecorderRef.current.stop) {
//         mediaRecorderRef.current.stop()
//       }
//       if (mediaRecorderRef.current.abort) {
//         mediaRecorderRef.current.abort()
//       }
//       mediaRecorderRef.current = null
//     }

//     if (audioContextRef.current && audioContextRef.current.state !== "closed") {
//       audioContextRef.current.close()
//       audioContextRef.current = null
//     }

//     // Clear audio queue and stop any playing audio
//     clearAudioQueue()

//     setTranslationSession(null)
//     setIsTranslationEnabled(false)
//     setTranslationStatus("")
//     setCurrentTranscription("")
//     setCurrentTranslation("")
//   }

//   useEffect(() => {
//     if (remoteVideoTracks.length === 0) return

//     remoteVideoTracks.forEach(({ track, publication }) => {
//       if (publication.source === "screen_share" || publication.source === "screen_share_audio") {
//         if (screenVideoRef.current && !track.attachedElements.includes(screenVideoRef.current)) {
//           track.attach(screenVideoRef.current)
//         }
//       } else if (publication.source === "camera" || publication.trackName === "teacher_camera" || !publication.source) {
//         if (hasScreenShare) {
//           if (cameraVideoRef.current && !track.attachedElements.includes(cameraVideoRef.current)) {
//             track.attach(cameraVideoRef.current)
//           }
//         } else {
//           if (cameraMainRef.current && !track.attachedElements.includes(cameraMainRef.current)) {
//             track.attach(cameraMainRef.current)
//           }
//         }
//       }
//     })
//   }, [remoteVideoTracks, hasScreenShare, hasCameraTrack])

//   const connectToLiveKitRoom = async (token, url) => {
//     try {
//       setConnectionStatus("Connecting...")
//       const room = new Room()

//       room.on(RoomEvent.Connected, () => {
//         setLivekitConnected(true)
//         setConnectionStatus("Connected")
//       })

//       room.on(RoomEvent.Disconnected, () => {
//         setLivekitConnected(false)
//         setConnectionStatus("Disconnected")
//         setIsViewingStream(false)
//       })

//       room.on(RoomEvent.ParticipantConnected, (participant) => {
//         setParticipants((prev) => [...prev, participant])
//       })

//       room.on(RoomEvent.ParticipantDisconnected, (participant) => {
//         setParticipants((prev) => prev.filter((p) => p.identity !== participant.identity))
//       })

//       room.on(RoomEvent.TrackSubscribed, (track, publication, participant) => {
//         if (track.kind === "video") {
//           setRemoteVideoTracks((prev) => {
//             const newTracks = [...prev, { track, participant, publication }]
//             setHasScreenShare(
//               newTracks.some(
//                 (t) => t.publication.source === "screen_share" || t.publication.source === "screen_share_audio",
//               ),
//             )
//             setHasCameraTrack(
//               newTracks.some(
//                 (t) =>
//                   t.publication.source === "camera" ||
//                   t.publication.trackName === "teacher_camera" ||
//                   !t.publication.source,
//               ),
//             )
//             return newTracks
//           })

//           if (publication.source === "screen_share" || publication.source === "screen_share_audio") {
//             if (screenVideoRef.current) track.attach(screenVideoRef.current)
//           } else if (
//             publication.source === "camera" ||
//             publication.trackName === "teacher_camera" ||
//             !publication.source
//           ) {
//             if (cameraVideoRef.current) track.attach(cameraVideoRef.current)
//             if (
//               !remoteVideoTracks.some(
//                 (t) => t.publication.source === "screen_share" || t.publication.source === "screen_share_audio",
//               ) &&
//               cameraMainRef.current
//             ) {
//               track.attach(cameraMainRef.current)
//             }
//           }
//         } else if (track.kind === "audio") {
//           const audioElement = track.attach()
//           setRemoteAudioTracks((prev) => [...prev, { track, participant, publication, audioElement }])
//         }
//       })

//       room.on(RoomEvent.TrackUnsubscribed, (track, publication, participant) => {
//         if (track.kind === "video") {
//           setRemoteVideoTracks((prev) => {
//             const newTracks = prev.filter((t) => t.track !== track)
//             setHasScreenShare(
//               newTracks.some(
//                 (t) => t.publication.source === "screen_share" || t.publication.source === "screen_share_audio",
//               ),
//             )
//             setHasCameraTrack(
//               newTracks.some(
//                 (t) =>
//                   t.publication.source === "camera" ||
//                   t.publication.trackName === "teacher_camera" ||
//                   !t.publication.source,
//               ),
//             )
//             return newTracks
//           })
//           track.detach()
//         } else if (track.kind === "audio") {
//           setRemoteAudioTracks((prev) => {
//             const trackInfo = prev.find((t) => t.track === track)
//             if (trackInfo && trackInfo.audioElement) {
//               track.detach(trackInfo.audioElement)
//             }
//             return prev.filter((t) => t.track !== track)
//           })
//         }
//       })

//       await room.connect(url, token)
//       setLivekitRoom(room)
//     } catch (err) {
//       setConnectionStatus("Connection failed: " + err.message)
//       alert("Failed to connect to stream: " + err.message)
//     }
//   }

//   const handleJoinStream = async (stream) => {
//     try {
//       const userId = sessionStorage.getItem("userId")
//       if (!userId) {
//         alert("Please login first to join the stream.")
//         return
//       }

//       if (!stream.session_id || !stream.teacher_id) {
//         alert("Invalid stream data. Please try again.")
//         return
//       }

//       const sessionData = { session_id: stream.session_id, teacher_id: stream.teacher_id }
//       const response = await joinStream(sessionData).unwrap()

//       console.log("🔍 JOIN STREAM RESPONSE:", response)

//       if (response.token && response.livekit_url) {
//         // CRITICAL FIX: Always use the session_id from the response, not the original stream
//         const correctSessionId = response.stream_info?.session_id || response.room_name || stream.session_id

//         const updatedStream = {
//           ...stream,
//           session_id: correctSessionId, // Use the correct session ID from backend
//         }

//         console.log("🔍 ORIGINAL STREAM SESSION ID:", stream.session_id)
//         console.log("🔍 BACKEND RESPONSE SESSION ID:", correctSessionId)
//         console.log("🔍 FINAL STREAM FOR VIEWER:", updatedStream)

//         // Ensure we're using the correct session ID
//         if (stream.session_id !== correctSessionId) {
//           console.log("⚠️ SESSION ID MISMATCH DETECTED!")
//           console.log(`⚠️ Original: ${stream.session_id}`)
//           console.log(`⚠️ Correct: ${correctSessionId}`)
//           console.log("✅ Using correct session ID from backend response")

//           // Show alert to user about the fix
//           alert(
//             `🔧 Session ID Fixed!\nOriginal: ${stream.session_id}\nCorrected: ${correctSessionId}\n\nChat should now work properly!`,
//           )
//         }

//         setCurrentStream(updatedStream)
//         setIsViewingStream(true)
//         await connectToLiveKitRoom(response.token, response.livekit_url)
//       } else {
//         alert("Invalid stream response. Missing connection credentials.")
//       }
//     } catch (error) {
//       alert("Failed to join stream: " + (error.data?.message || "Unknown error"))
//     }
//   }

//   const handleLeaveStream = () => {
//     if (livekitRoom) {
//       livekitRoom.disconnect()
//       setLivekitRoom(null)
//     }

//     cleanupTranslation()
//     setIsViewingStream(false)
//     setCurrentStream(null)
//     setLivekitConnected(false)
//     setParticipants([])
//     setRemoteVideoTracks([])
//     setRemoteAudioTracks([])
//     setConnectionStatus("")
//     setHasScreenShare(false)
//     setHasCameraTrack(false)
//   }

//   const startTranslation = async () => {
//     try {
//       setTranslationStatus("Starting translation...")
//       const userId = sessionStorage.getItem("userId")
//       if (!userId) throw new Error("User not authenticated")

//       const data = await startTranslationSession({
//         user_id: userId,
//         stream_session_id: currentStream.session_id,
//         source_language: sourceLanguage,
//         target_language: targetLanguage,
//       }).unwrap()

//       setTranslationSession(data.session_id)
//       await connectTranslationWebSocket(data.websocket_url)
//       await new Promise((resolve) => setTimeout(resolve, 2000))

//       const ws = translationWebSocketRef.current || translationWebSocket
//       if (!ws || ws.readyState !== WebSocket.OPEN) throw new Error("WebSocket not ready")

//       await startAudioCapture()
//       setIsTranslationEnabled(true)
//       setTranslationStatus("Translation active - listening for audio")
//     } catch (error) {
//       setTranslationStatus(`Error: ${error.message}`)
//     }
//   }

//   const stopTranslation = async () => {
//     try {
//       setTranslationStatus("Stopping translation...")
//       if (translationSession) {
//         await stopTranslationSession({ session_id: translationSession }).unwrap()
//       }
//       cleanupTranslation()
//       setTranslationStatus("Translation stopped")
//     } catch (error) {
//       setTranslationStatus(`Error: ${error.message}`)
//     }
//   }

//   const connectTranslationWebSocket = async (websocketUrl) => {
//     return new Promise((resolve, reject) => {
//       const ws = new WebSocket(websocketUrl)

//       ws.onopen = () => {
//         setTranslationWebSocket(ws)
//         translationWebSocketRef.current = ws
//         setTranslationStatus("WebSocket connected - ready for audio")
//         resolve()
//       }

//       ws.onmessage = (event) => {
//         const data = JSON.parse(event.data)
//         handleTranslationMessage(data)
//       }

//       ws.onerror = (error) => {
//         setTranslationStatus(`WebSocket error: ${error.message || "Connection failed"}`)
//         reject(error)
//       }

//       ws.onclose = () => {
//         setTranslationWebSocket(null)
//         setTranslationStatus("WebSocket disconnected")
//       }
//     })
//   }

//   const handleTranslationMessage = (data) => {
//     console.log("📨 Received WebSocket message:", JSON.stringify(data))
//     console.log("🔄 Handling translation message:", data.type, data)

//     switch (data.type) {
//       case "connection_established":
//         setTranslationStatus("Connected and ready")
//         break
//       case "transcription":
//         setCurrentTranscription(data.text)
//         setTranslationStatus("Transcribing...")
//         break
//       case "translation":
//         console.log("🌐 Received translation:", data.translated_text)
//         setCurrentTranslation(data.translated_text)
//         setTranslationStatus("Translating...")
//         break
//       case "translated_audio":
//         console.log("🔊 Received translated audio, length:", data.audio_data?.length)
//         setTranslationStatus("Queuing audio...")
//         addToAudioQueue(data.audio_data)
//         break
//       case "error":
//         setTranslationStatus(`Error: ${data.message}`)
//         break
//       default:
//         console.log("❓ Unknown message type:", data.type)
//         break
//     }
//   }

//   const startAudioCapture = async () => {
//     try {
//       setTranslationStatus("Starting audio capture...")

//       if (remoteAudioTracks.length === 0) throw new Error("No audio tracks available")

//       const audioTrack = remoteAudioTracks[0].track
//       const mediaStream = new MediaStream([audioTrack.mediaStreamTrack])

//       const audioContext = new (window.AudioContext || window.webkitAudioContext)({ sampleRate: 16000 })
//       audioContextRef.current = audioContext

//       const source = audioContext.createMediaStreamSource(mediaStream)
//       const bufferSize = 4096
//       const processor = audioContext.createScriptProcessor(bufferSize, 1, 1)

//       let audioBuffer = []
//       let bufferDuration = 0
//       const targetDuration = 2
//       const sampleRate = audioContext.sampleRate

//       processor.onaudioprocess = (event) => {
//         const inputData = event.inputBuffer.getChannelData(0)
//         audioBuffer.push(new Float32Array(inputData))
//         bufferDuration += inputData.length / sampleRate

//         if (bufferDuration >= targetDuration) {
//           console.log("📊 Processing", bufferDuration.toFixed(2) + "s", "of audio data")

//           const totalLength = audioBuffer.reduce((sum, chunk) => sum + chunk.length, 0)
//           const combinedBuffer = new Float32Array(totalLength)
//           let offset = 0

//           for (const chunk of audioBuffer) {
//             combinedBuffer.set(chunk, offset)
//             offset += chunk.length
//           }

//           const wavBuffer = convertToWAV(combinedBuffer, 16000)
//           console.log("🎵 Created WAV file:", wavBuffer.byteLength, "bytes")

//           const ws = translationWebSocketRef.current || translationWebSocket
//           console.log("🔍 WebSocket check:", ws)

//           if (ws && ws.readyState === WebSocket.OPEN) {
//             console.log("📤 Sending audio data to translation service...")
//             ws.send(wavBuffer)
//             console.log("✅ Audio data sent successfully")
//           } else {
//             console.error("❌ WebSocket not ready, state:", ws?.readyState)
//           }

//           audioBuffer = []
//           bufferDuration = 0
//         }
//       }

//       source.connect(processor)
//       processor.connect(audioContext.destination)

//       mediaRecorderRef.current = processor
//       setTranslationStatus("Capturing audio from stream...")
//     } catch (error) {
//       setTranslationStatus(`Audio capture error: ${error.message}`)
//       await startSpeechRecognitionFallback()
//     }
//   }

//   const convertToWAV = (audioData, sampleRate) => {
//     const length = audioData.length
//     const buffer = new ArrayBuffer(44 + length * 2)
//     const view = new DataView(buffer)

//     const writeString = (offset, string) => {
//       for (let i = 0; i < string.length; i++) view.setUint8(offset + i, string.charCodeAt(i))
//     }

//     writeString(0, "RIFF")
//     view.setUint32(4, 36 + length * 2, true)
//     writeString(8, "WAVE")
//     writeString(12, "fmt ")
//     view.setUint32(16, 16, true)
//     view.setUint16(20, 1, true)
//     view.setUint16(22, 1, true)
//     view.setUint32(24, sampleRate, true)
//     view.setUint32(28, sampleRate * 2, true)
//     view.setUint16(32, 2, true)
//     view.setUint16(34, 16, true)
//     writeString(36, "data")
//     view.setUint32(40, length * 2, true)

//     let offset = 44
//     for (let i = 0; i < length; i++) {
//       const sample = Math.max(-1, Math.min(1, audioData[i]))
//       view.setInt16(offset, sample * 0x7fff, true)
//       offset += 2
//     }

//     return buffer
//   }

//   const startSpeechRecognitionFallback = async () => {
//     try {
//       setTranslationStatus("Using speech recognition fallback...")

//       const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition
//       if (!SpeechRecognition) throw new Error("Speech recognition not supported")

//       const recognition = new SpeechRecognition()
//       recognition.continuous = true
//       recognition.interimResults = true
//       recognition.lang =
//         sourceLanguage === "en"
//           ? "en-US"
//           : sourceLanguage === "ta"
//             ? "ta-IN"
//             : sourceLanguage === "hi"
//               ? "hi-IN"
//               : sourceLanguage === "te"
//                 ? "te-IN"
//                 : sourceLanguage === "kn"
//                   ? "kn-IN"
//                   : "en-US"

//       recognition.onresult = async (event) => {
//         const result = event.results[event.results.length - 1]
//         const transcript = result[0].transcript

//         if (result.isFinal && transcript.trim()) {
//           setCurrentTranscription(transcript)
//           const ws = translationWebSocketRef.current || translationWebSocket
//           if (ws && ws.readyState === WebSocket.OPEN) {
//             ws.send(
//               JSON.stringify({
//                 type: "transcript",
//                 text: transcript,
//                 source_language: sourceLanguage,
//                 target_language: targetLanguage,
//                 session_id: translationSession,
//               }),
//             )
//           }
//         }
//       }

//       recognition.onerror = (event) => console.error("Speech recognition error:", event.error)
//       recognition.onend = () => {
//         if (isTranslationEnabled) setTimeout(() => recognition.start(), 500)
//       }

//       mediaRecorderRef.current = recognition
//       recognition.start()
//     } catch (error) {
//       setTranslationStatus(`All audio capture methods failed: ${error.message}`)
//     }
//   }

//   // Audio playback function for queue system
//   const playAudioFromQueue = async (audioDataBase64) => {
//     return new Promise(async (resolve) => {
//       try {
//         console.log("🎵 Playing queued audio, data length:", audioDataBase64?.length)
//         setTranslationStatus("Playing audio...")

//         if (!audioDataBase64) {
//           console.error("❌ No audio data provided")
//           setIsPlayingTranslatedAudio(false)
//           resolve()
//           return
//         }

//         // Create audio context if not exists
//         if (!audioContextRef.current) {
//           audioContextRef.current = new (window.AudioContext || window.webkitAudioContext)()
//         }

//         // Resume audio context if suspended
//         if (audioContextRef.current.state === "suspended") {
//           await audioContextRef.current.resume()
//         }

//         // Method 1: Try Web Audio API with base64 decoding
//         try {
//           // Convert base64 to array buffer
//           const binaryString = atob(audioDataBase64)
//           const audioData = new Uint8Array(binaryString.length)
//           for (let i = 0; i < binaryString.length; i++) {
//             audioData[i] = binaryString.charCodeAt(i)
//           }

//           console.log("🔊 Decoded audio data, size:", audioData.length, "bytes")

//           const audioBuffer = await audioContextRef.current.decodeAudioData(audioData.buffer)
//           const source = audioContextRef.current.createBufferSource()
//           source.buffer = audioBuffer
//           source.connect(audioContextRef.current.destination)

//           // Store reference to current audio source
//           currentAudioSourceRef.current = source
//           setIsPlayingTranslatedAudio(true)

//           source.onended = () => {
//             console.log("🎵 Web Audio playback ended")
//             setIsPlayingTranslatedAudio(false)
//             currentAudioSourceRef.current = null
//             setTranslationStatus("Translation active - listening for audio")
//             resolve()
//           }

//           source.start()
//           console.log("✅ Web Audio playback started successfully")
//           return
//         } catch (webAudioError) {
//           console.warn("⚠️ Web Audio API failed:", webAudioError.message)

//           // Method 2: Fallback to HTML5 Audio element
//           try {
//             const audioBlob = new Blob(
//               [
//                 new Uint8Array(
//                   atob(audioDataBase64)
//                     .split("")
//                     .map((c) => c.charCodeAt(0)),
//                 ),
//               ],
//               { type: "audio/wav" },
//             )

//             const audioUrl = URL.createObjectURL(audioBlob)
//             const audio = new Audio(audioUrl)

//             // Store reference to current HTML audio
//             currentHtmlAudioRef.current = audio
//             setIsPlayingTranslatedAudio(true)

//             audio.onended = () => {
//               console.log("🎵 HTML5 Audio playback ended")
//               setIsPlayingTranslatedAudio(false)
//               currentHtmlAudioRef.current = null
//               URL.revokeObjectURL(audioUrl)
//               setTranslationStatus("Translation active - listening for audio")
//               resolve()
//             }

//             audio.onerror = (error) => {
//               console.error("❌ HTML5 Audio error:", error)
//               setIsPlayingTranslatedAudio(false)
//               currentHtmlAudioRef.current = null
//               URL.revokeObjectURL(audioUrl)
//               setTranslationStatus("Audio playback error")
//               resolve()
//             }

//             await audio.play()
//             console.log("✅ HTML5 Audio playback started successfully")
//           } catch (htmlAudioError) {
//             console.error("❌ HTML5 Audio also failed:", htmlAudioError.message)
//             setIsPlayingTranslatedAudio(false)
//             setTranslationStatus(`Audio playback error: ${htmlAudioError.message}`)
//             resolve()
//           }
//         }
//       } catch (error) {
//         console.error("🔊 Error playing queued audio:", error)
//         setIsPlayingTranslatedAudio(false)
//         setTranslationStatus(`Audio playback error: ${error.message}`)
//         resolve()
//       }
//     })
//   }

//   // Chat functions using slice
//   const handleSendChatMessage = async () => {
//     if (!newMessage.trim() || !socketConnected || !currentStream) return

//     const messageData = {
//       session_id: currentStream.session_id,
//       message: newMessage.trim(),
//       sender_id: sessionStorage.getItem("userId"),
//       sender_name: sessionStorage.getItem("name") || "Viewer",
//     }

//     console.log("📤 VIEWER SENDING MESSAGE WITH SESSION ID:", currentStream.session_id)
//     console.log("📤 VIEWER MESSAGE DATA:", messageData)

//     try {
//       await sendChatMessage(messageData).unwrap()
//       console.log("✅ VIEWER: Message sent successfully")
//       setNewMessage("")
//       // Immediately load chat history to see the new message
//       setTimeout(loadChatHistory, 500)
//     } catch (error) {
//       console.error("❌ VIEWER: Error sending message:", error.message)
//     }
//   }

//   const handleKeyDown = (e) => {
//     if (e.key === "Enter" && !e.shiftKey) {
//       e.preventDefault()
//       handleSendChatMessage()
//     }
//   }

//   const toggleChat = () => {
//     setIsChatOpen(!isChatOpen)
//     if (!isChatOpen) {
//       setUnreadMessages(0)
//     }
//   }

//   const formatMessageTime = (timestamp) => {
//     return new Date(timestamp).toLocaleTimeString([], {
//       hour: "2-digit",
//       minute: "2-digit",
//     })
//   }

//   const getRoleColor = (role) => {
//     switch (role) {
//       case "kota_teacher":
//       case "faculty":
//         return "text-emerald-600"
//       case "student":
//         return "text-blue-600"
//       case "center_counselor":
//         return "text-purple-600"
//       default:
//         return "text-gray-600"
//     }
//   }

//   const getRoleBadge = (role) => {
//     switch (role) {
//       case "kota_teacher":
//       case "faculty":
//         return "Teacher"
//       case "student":
//         return "Student"
//       case "center_counselor":
//         return "Counselor"
//       default:
//         return "User"
//     }
//   }

//   // Test Socket.IO connection function
//   const testSocketConnection = async () => {
//     console.log("🧪 Testing Socket.IO connection...")
//     const testUrls = ["https://sasthra.in"]

//     for (const url of testUrls) {
//       try {
//         console.log(`🔍 Testing: ${url}`)
//         const testSocket = io(url, {
//           transports: ["polling"], // Start with polling only
//           timeout: 5000,
//           forceNew: true,
//         })

//         await new Promise((resolve, reject) => {
//           const timeout = setTimeout(() => {
//             testSocket.disconnect()
//             reject(new Error("Timeout"))
//           }, 5000)

//           testSocket.on("connect", () => {
//             clearTimeout(timeout)
//             console.log(`✅ Test connection successful: ${url}`)
//             testSocket.disconnect()
//             resolve(url)
//           })

//           testSocket.on("connect_error", (error) => {
//             clearTimeout(timeout)
//             console.log(`❌ Test connection failed: ${url} - ${error.message}`)
//             testSocket.disconnect()
//             reject(error)
//           })
//         })

//         // If we get here, connection was successful
//         return url
//       } catch (error) {
//         console.log(`❌ Test failed for ${url}:`, error.message)
//         continue
//       }
//     }

//     throw new Error("All test connections failed")
//   }

//   // Add a test message function
//   const sendTestMessage = () => {
//     if (socket && socketConnected && currentStream) {
//       const testMessage = {
//         session_id: currentStream.session_id,
//         message: "Test message from viewer",
//         sender_id: sessionStorage.getItem("userId"),
//         sender_name: sessionStorage.getItem("name") || "Viewer",
//       }

//       console.log("🧪 Sending test message:", testMessage)
//       socket.emit("chat_message", testMessage)
//     } else {
//       console.log("❌ Cannot send test message - socket not connected or no stream")
//     }
//   }

//   // Health check function
//   const checkBackendHealth = async () => {
//     const healthUrls = [
//       "https://sasthra.in:8012/health",
//       "https://sasthra.in/health",
//       "http://sasthra.in:8012/health",
//       "http://localhost:8012/health",
//     ]

//     console.log("🏥 Checking backend health...")

//     for (const url of healthUrls) {
//       try {
//         console.log(`🔍 Checking: ${url}`)
//         const response = await fetch(url, {
//           method: "GET",
//           headers: {
//             "Content-Type": "application/json",
//           },
//         })

//         if (response.ok) {
//           const data = await response.json()
//           console.log(`✅ Backend healthy at: ${url}`, data)
//           return { url, data }
//         }
//       } catch (error) {
//         console.log(`❌ Health check failed for ${url}:`, error.message)
//         continue
//       }
//     }

//     throw new Error("Backend health check failed for all URLs")
//   }

//   const formatUptime = (uptime) => {
//     const hours = Math.floor(uptime / 3600)
//     const minutes = Math.floor((uptime % 3600) / 60)
//     const seconds = Math.floor(uptime % 60)
//     if (hours > 0) return `${hours}h ${minutes}m ${seconds}s`
//     if (minutes > 0) return `${minutes}m ${seconds}s`
//     return `${seconds}s`
//   }

//   const formatDateTime = (dateString) => new Date(dateString).toLocaleString()

//   if (isLoading)
//     return (
//       <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
//         <div className="text-center">
//           <div className="relative">
//             <div className="w-20 h-20 border-4 border-purple-200 border-t-purple-600 rounded-full animate-spin mx-auto"></div>
//             <div
//               className="absolute inset-0 w-20 h-20 border-4 border-transparent border-r-pink-500 rounded-full animate-spin mx-auto"
//               style={{ animationDirection: "reverse", animationDuration: "1.5s" }}
//             ></div>
//           </div>
//           <p className="text-white text-lg font-medium mt-6 animate-pulse">Loading Live Streams...</p>
//           <div className="flex justify-center mt-4 space-x-1">
//             <div className="w-2 h-2 bg-purple-400 rounded-full animate-bounce"></div>
//             <div className="w-2 h-2 bg-pink-400 rounded-full animate-bounce" style={{ animationDelay: "0.1s" }}></div>
//             <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce" style={{ animationDelay: "0.2s" }}></div>
//           </div>
//         </div>
//       </div>
//     )

//   if (error)
//     return (
//       <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-red-50 to-red-100">
//         <div className="bg-white border border-red-200 rounded-2xl shadow-2xl p-8 max-w-md mx-4 transform hover:scale-105 transition-transform duration-300">
//           <div className="flex items-center justify-center w-16 h-16 bg-red-100 rounded-full mx-auto mb-4">
//             <svg className="h-8 w-8 text-red-600" viewBox="0 0 20 20" fill="currentColor">
//               <path
//                 fillRule="evenodd"
//                 d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
//                 clipRule="evenodd"
//               />
//             </svg>
//           </div>
//           <h3 className="text-xl font-bold text-red-800 text-center mb-2">Connection Error</h3>
//           <p className="text-red-700 text-center mb-4">{error.message || "Failed to load active streams"}</p>
//           <button
//             onClick={() => trigger()}
//             className="w-full bg-red-600 hover:bg-red-700 text-white font-semibold py-3 px-4 rounded-lg transition-colors duration-200"
//           >
//             Try Again
//           </button>
//         </div>
//       </div>
//     )

//   const activeStreams = data?.active_streams || []

//   if (isViewingStream && currentStream)
//     return (
//       <div className="min-h-screen bg-gradient-to-br from-gray-900 via-slate-900 to-black">
//         {/* Enhanced Header */}
//         <div className="bg-gradient-to-r from-gray-800 via-gray-900 to-black text-white p-4 shadow-2xl border-b border-gray-700">
//           <div className="flex items-center justify-between">
//             <div className="flex items-center space-x-4">
//               <button
//                 onClick={handleLeaveStream}
//                 className="group bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white px-6 py-3 rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-red-500/25 flex items-center space-x-2"
//               >
//                 <svg
//                   className="w-5 h-5 group-hover:-translate-x-1 transition-transform duration-300"
//                   fill="none"
//                   viewBox="0 0 24 24"
//                   stroke="currentColor"
//                 >
//                   <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
//                 </svg>
//                 <span className="font-semibold">Leave Stream</span>
//               </button>
//               <div className="border-l border-gray-600 pl-4">
//                 <h1 className="text-2xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
//                   Live Stream Viewer
//                 </h1>
//                 <p className="text-sm text-gray-300 flex items-center">
//                   <span className="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></span>
//                   Session: {currentStream.session_id}
//                 </p>
//               </div>
//             </div>

//             <div className="flex items-center space-x-6">
//               <div className="flex items-center bg-gray-800 rounded-lg px-4 py-2">
//                 <div
//                   className={`w-3 h-3 rounded-full mr-3 ${livekitConnected ? "bg-green-400 animate-pulse" : "bg-red-400"}`}
//                 ></div>
//                 <span className="text-sm font-medium">{connectionStatus || "Connecting..."}</span>
//               </div>
//               <div className="flex items-center bg-gray-800 rounded-lg px-4 py-2">
//                 <svg className="w-4 h-4 mr-2 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
//                   <path
//                     strokeLinecap="round"
//                     strokeLinejoin="round"
//                     strokeWidth={2}
//                     d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"
//                   />
//                 </svg>
//                 <span className="text-sm font-medium">{participants.length + 1} Participants</span>
//               </div>
//             </div>
//           </div>
//         </div>

//         <div className="flex h-[calc(100vh-80px)]">
//           {/* Enhanced Video Area */}
//           <div className="flex-1 relative bg-black">
//             <div className="w-full h-full flex items-center justify-center relative overflow-hidden">
//               {/* Main Video Display */}
//               <video
//                 ref={screenVideoRef}
//                 autoPlay
//                 playsInline
//                 className="w-full h-full object-contain transition-all duration-500 ease-in-out"
//                 style={{ maxHeight: "100%", maxWidth: "100%" }}
//               />

//               {!hasScreenShare && hasCameraTrack && (
//                 <video
//                   ref={cameraMainRef}
//                   autoPlay
//                   playsInline
//                   className="w-full h-full object-contain absolute inset-0 transition-all duration-500 ease-in-out"
//                   style={{ maxHeight: "100%", maxWidth: "100%" }}
//                 />
//               )}

//               {/* Loading State */}
//               {remoteVideoTracks.length === 0 && (
//                 <div className="absolute inset-0 flex items-center justify-center text-white bg-gradient-to-br from-gray-900/50 to-black/50 backdrop-blur-sm">
//                   <div className="text-center">
//                     <div className="relative mb-8">
//                       <div className="w-20 h-20 border-4 border-blue-200 border-t-blue-500 rounded-full animate-spin mx-auto"></div>
//                       <div
//                         className="absolute inset-0 w-20 h-20 border-4 border-transparent border-r-purple-500 rounded-full animate-spin mx-auto"
//                         style={{ animationDirection: "reverse", animationDuration: "1.5s" }}
//                       ></div>
//                     </div>
//                     <p className="text-2xl font-bold mb-2 bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
//                       Waiting for stream...
//                     </p>
//                     <p className="text-gray-300 flex items-center justify-center">
//                       <span
//                         className={`w-2 h-2 rounded-full mr-2 ${livekitConnected ? "bg-green-400 animate-pulse" : "bg-red-400"}`}
//                       ></span>
//                       {livekitConnected ? "Connected to room" : "Connecting..."}
//                     </p>
//                   </div>
//                 </div>
//               )}
//             </div>

//             {/* Picture-in-Picture Camera */}
//             {hasScreenShare && hasCameraTrack && (
//               <div className="absolute bottom-6 right-6 w-72 h-52 bg-gray-900 rounded-2xl overflow-hidden border-2 border-gray-600 shadow-2xl transform hover:scale-105 transition-all duration-300">
//                 <video ref={cameraVideoRef} autoPlay playsInline muted className="w-full h-full object-cover" />
//                 <div className="absolute bottom-3 left-3 bg-black/70 backdrop-blur-sm text-white text-xs px-3 py-1 rounded-full flex items-center">
//                   <div
//                     className={`w-2 h-2 rounded-full mr-2 ${hasCameraTrack ? "bg-green-400 animate-pulse" : "bg-red-400"}`}
//                   ></div>
//                   Teacher Camera
//                 </div>
//               </div>
//             )}

//             {/* Floating Chat Button */}
//             {!isChatOpen && (
//               <button
//                 onClick={toggleChat}
//                 className="absolute bottom-6 left-6 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white p-4 rounded-full shadow-2xl transition-all duration-300 transform hover:scale-110 hover:shadow-blue-500/25 flex items-center space-x-2 group"
//               >
//                 <svg
//                   className="w-6 h-6 group-hover:rotate-12 transition-transform duration-300"
//                   fill="none"
//                   viewBox="0 0 24 24"
//                   stroke="currentColor"
//                 >
//                   <path
//                     strokeLinecap="round"
//                     strokeLinejoin="round"
//                     strokeWidth={2}
//                     d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.959 8.959 0 01-4.906-1.405L3 21l2.595-5.094A8.959 8.959 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z"
//                   />
//                 </svg>
//                 {unreadMessages > 0 && (
//                   <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs px-2 py-1 rounded-full animate-bounce shadow-lg">
//                     {unreadMessages}
//                   </span>
//                 )}
//               </button>
//             )}
//           </div>

//           {/* Enhanced Sidebar */}
//           <div className="w-96 bg-gradient-to-b from-gray-900 via-gray-800 to-gray-900 text-white overflow-y-auto shadow-2xl border-l border-gray-700">
//             <div className="p-6 space-y-8">
//               {/* Stream Information Card */}
//               <div className="bg-gradient-to-br from-gray-800 to-gray-900 rounded-2xl p-6 border border-gray-700 shadow-xl">
//                 <h3 className="text-xl font-bold mb-4 flex items-center">
//                   <svg className="w-6 h-6 mr-2 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
//                     <path
//                       strokeLinecap="round"
//                       strokeLinejoin="round"
//                       strokeWidth={2}
//                       d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
//                     />
//                   </svg>
//                   Stream Information
//                 </h3>
//                 <div className="space-y-3">
//                   <div className="flex justify-between items-center p-3 bg-gray-700/50 rounded-lg">
//                     <span className="text-gray-300">Teacher ID:</span>
//                     <span className="font-semibold text-blue-400">{currentStream.teacher_id}</span>
//                   </div>
//                   <div className="flex justify-between items-center p-3 bg-gray-700/50 rounded-lg">
//                     <span className="text-gray-300">Quality:</span>
//                     <span className="font-semibold text-green-400">{currentStream.quality || "Standard"}</span>
//                   </div>
//                   <div className="flex justify-between items-center p-3 bg-gray-700/50 rounded-lg">
//                     <span className="text-gray-300">Viewers:</span>
//                     <span className="font-semibold text-purple-400">{currentStream.viewer_count || 0}</span>
//                   </div>
//                 </div>
//               </div>

//               {/* Features Card */}
//               <div className="bg-gradient-to-br from-gray-800 to-gray-900 rounded-2xl p-6 border border-gray-700 shadow-xl">
//                 <h3 className="text-xl font-bold mb-4 flex items-center">
//                   <svg className="w-6 h-6 mr-2 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
//                     <path
//                       strokeLinecap="round"
//                       strokeLinejoin="round"
//                       strokeWidth={2}
//                       d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
//                     />
//                   </svg>
//                   Features
//                 </h3>
//                 <div className="space-y-3">
//                   {currentStream.features?.screen_sharing && (
//                     <div className="flex items-center p-3 bg-green-500/10 border border-green-500/20 rounded-lg">
//                       <div className="w-3 h-3 bg-green-400 rounded-full mr-3 animate-pulse"></div>
//                       <span className="text-green-400 font-medium">Screen Sharing Active</span>
//                     </div>
//                   )}
//                   {currentStream.features?.chat_enabled && (
//                     <div className="flex items-center p-3 bg-blue-500/10 border border-blue-500/20 rounded-lg">
//                       <div className="w-3 h-3 bg-blue-400 rounded-full mr-3 animate-pulse"></div>
//                       <span className="text-blue-400 font-medium">Chat Available</span>
//                     </div>
//                   )}
//                   {currentStream.features?.recording_enabled && (
//                     <div className="flex items-center p-3 bg-red-500/10 border border-red-500/20 rounded-lg">
//                       <div className="w-3 h-3 bg-red-400 rounded-full mr-3 animate-pulse"></div>
//                       <span className="text-red-400 font-medium">Recording Active</span>
//                     </div>
//                   )}
//                 </div>
//               </div>

//               {/* Participants Card */}
//               <div className="bg-gradient-to-br from-gray-800 to-gray-900 rounded-2xl p-6 border border-gray-700 shadow-xl">
//                 <h3 className="text-xl font-bold mb-4 flex items-center">
//                   <svg className="w-6 h-6 mr-2 text-purple-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
//                     <path
//                       strokeLinecap="round"
//                       strokeLinejoin="round"
//                       strokeWidth={2}
//                       d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"
//                     />
//                   </svg>
//                   Participants ({participants.length + 1})
//                 </h3>
//                 <div className="space-y-3 max-h-40 overflow-y-auto">
//                   <div className="flex items-center p-3 bg-gradient-to-r from-green-500/10 to-emerald-500/10 border border-green-500/20 rounded-lg">
//                     <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center text-sm font-bold mr-3">
//                       T
//                     </div>
//                     <div className="flex-1">
//                       <div className="font-semibold text-green-400">Teacher</div>
//                       <div className="text-xs text-gray-400">Host</div>
//                     </div>
//                     <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
//                   </div>
//                   {participants.map((participant, index) => (
//                     <div
//                       key={participant.identity || index}
//                       className="flex items-center p-3 bg-gradient-to-r from-blue-500/10 to-purple-500/10 border border-blue-500/20 rounded-lg"
//                     >
//                       <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-sm font-bold mr-3">
//                         {participant.identity?.charAt(0)?.toUpperCase() || "U"}
//                       </div>
//                       <div className="flex-1">
//                         <div className="font-semibold text-blue-400">{participant.identity || "Unknown"}</div>
//                         <div className="text-xs text-gray-400">Viewer</div>
//                       </div>
//                       <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></div>
//                     </div>
//                   ))}
//                 </div>
//               </div>

//               {/* Connection Status Card */}
//               <div className="bg-gradient-to-br from-gray-800 to-gray-900 rounded-2xl p-6 border border-gray-700 shadow-xl">
//                 <h3 className="text-xl font-bold mb-4 flex items-center">
//                   <svg className="w-6 h-6 mr-2 text-yellow-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
//                     <path
//                       strokeLinecap="round"
//                       strokeLinejoin="round"
//                       strokeWidth={2}
//                       d="M8.111 16.404a5.5 5.5 0 017.778 0M12 20h.01m-7.08-7.071c3.904-3.905 10.236-3.905 14.141 0M1.394 9.393c5.857-5.857 15.355-5.857 21.213 0"
//                     />
//                   </svg>
//                   Connection Status
//                 </h3>
//                 <div className="space-y-3">
//                   <div
//                     className={`flex items-center p-3 rounded-lg ${livekitConnected ? "bg-green-500/10 border border-green-500/20" : "bg-red-500/10 border border-red-500/20"}`}
//                   >
//                     <div
//                       className={`w-3 h-3 rounded-full mr-3 ${livekitConnected ? "bg-green-400 animate-pulse" : "bg-red-400"}`}
//                     ></div>
//                     <span className={`font-medium ${livekitConnected ? "text-green-400" : "text-red-400"}`}>
//                       {livekitConnected ? "Connected" : "Disconnected"}
//                     </span>
//                   </div>
//                   <div className="grid grid-cols-2 gap-3 text-sm">
//                     <div className="bg-gray-700/50 p-3 rounded-lg text-center">
//                       <div className="text-gray-400">Video Tracks</div>
//                       <div className="text-xl font-bold text-blue-400">{remoteVideoTracks.length}</div>
//                     </div>
//                     <div className="bg-gray-700/50 p-3 rounded-lg text-center">
//                       <div className="text-gray-400">Audio Tracks</div>
//                       <div className="text-xl font-bold text-green-400">{remoteAudioTracks.length}</div>
//                     </div>
//                   </div>
//                   <div className="flex justify-between text-sm">
//                     <span className="text-gray-400">Screen Share:</span>
//                     <span className={hasScreenShare ? "text-green-400" : "text-gray-500"}>
//                       {hasScreenShare ? "Active ✅" : "Inactive ❌"}
//                     </span>
//                   </div>
//                   <div className="flex justify-between text-sm">
//                     <span className="text-gray-400">Camera:</span>
//                     <span className={hasCameraTrack ? "text-green-400" : "text-gray-500"}>
//                       {hasCameraTrack ? "Active ✅" : "Inactive ❌"}
//                     </span>
//                   </div>
//                 </div>
//               </div>

//               {/* Enhanced Translation Card */}
//               <div className="bg-gradient-to-br from-indigo-900/50 to-purple-900/50 rounded-2xl p-6 border border-indigo-500/20 shadow-xl">
//                 <h3 className="text-xl font-bold mb-4 flex items-center">
//                   <svg className="w-6 h-6 mr-2 text-indigo-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
//                     <path
//                       strokeLinecap="round"
//                       strokeLinejoin="round"
//                       strokeWidth={2}
//                       d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129"
//                     />
//                   </svg>
//                   Live Translation
//                 </h3>

//                 <div className="space-y-4">
//                   <div className="flex items-center justify-between">
//                     <span className="text-gray-300 font-medium">Enable Translation</span>
//                     <button
//                       onClick={isTranslationEnabled ? stopTranslation : startTranslation}
//                       disabled={!remoteAudioTracks.length}
//                       className={`px-4 py-2 rounded-xl font-semibold transition-all duration-300 transform hover:scale-105 ${
//                         isTranslationEnabled
//                           ? "bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white shadow-lg hover:shadow-red-500/25"
//                           : "bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white disabled:from-gray-600 disabled:to-gray-700 disabled:cursor-not-allowed shadow-lg hover:shadow-green-500/25"
//                       }`}
//                     >
//                       {isTranslationEnabled ? "Stop" : "Start"}
//                     </button>
//                   </div>

//                   <div className="grid grid-cols-2 gap-3">
//                     <div>
//                       <label className="block text-xs text-gray-400 mb-2 font-medium">From Language</label>
//                       <select
//                         value={sourceLanguage}
//                         onChange={(e) => setSourceLanguage(e.target.value)}
//                         disabled={isTranslationEnabled}
//                         className="w-full bg-gray-800 border border-gray-600 rounded-lg px-3 py-2 text-sm text-white disabled:opacity-50 focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200"
//                       >
//                         {availableLanguages.map((lang) => (
//                           <option key={lang.code} value={lang.code}>
//                             {lang.flag} {lang.name}
//                           </option>
//                         ))}
//                       </select>
//                     </div>
//                     <div>
//                       <label className="block text-xs text-gray-400 mb-2 font-medium">To Language</label>
//                       <select
//                         value={targetLanguage}
//                         onChange={(e) => setTargetLanguage(e.target.value)}
//                         disabled={isTranslationEnabled}
//                         className="w-full bg-gray-800 border border-gray-600 rounded-lg px-3 py-2 text-sm text-white disabled:opacity-50 focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200"
//                       >
//                         {availableLanguages.map((lang) => (
//                           <option key={lang.code} value={lang.code}>
//                             {lang.flag} {lang.name}
//                           </option>
//                         ))}
//                       </select>
//                     </div>
//                   </div>

//                   {/* Translation Status */}
//                   <div className="space-y-2">
//                     <div
//                       className={`flex items-center p-3 rounded-lg ${isTranslationEnabled ? "bg-green-500/10 border border-green-500/20" : "bg-gray-700/50"}`}
//                     >
//                       <div
//                         className={`w-3 h-3 rounded-full mr-3 ${isTranslationEnabled ? "bg-green-400 animate-pulse" : "bg-gray-400"}`}
//                       ></div>
//                       <span className="text-sm font-medium">
//                         {translationStatus || (isTranslationEnabled ? "Translation Active" : "Translation Inactive")}
//                       </span>
//                     </div>

//                     {isPlayingTranslatedAudio && (
//                       <div className="flex items-center p-3 bg-blue-500/10 border border-blue-500/20 rounded-lg">
//                         <div className="w-3 h-3 bg-blue-400 rounded-full mr-3 animate-pulse"></div>
//                         <span className="text-sm font-medium text-blue-400">Playing Translated Audio</span>
//                       </div>
//                     )}

//                     {audioQueueLength > 0 && (
//                       <div className="flex items-center p-3 bg-yellow-500/10 border border-yellow-500/20 rounded-lg">
//                         <div className="w-3 h-3 bg-yellow-400 rounded-full mr-3 animate-pulse"></div>
//                         <span className="text-sm font-medium text-yellow-400">
//                           Audio Queue: {audioQueueLength} pending
//                         </span>
//                       </div>
//                     )}
//                   </div>

//                   {/* Translation Details */}
//                   {isTranslationEnabled && (
//                     <div className="bg-gray-800/50 rounded-lg p-3 space-y-2 text-xs">
//                       <div className="flex justify-between">
//                         <span className="text-gray-400">WebSocket:</span>
//                         <span
//                           className={
//                             translationWebSocket
//                               ? translationWebSocket.readyState === 1
//                                 ? "text-green-400"
//                                 : translationWebSocket.readyState === 0
//                                   ? "text-yellow-400"
//                                   : translationWebSocket.readyState === 2
//                                     ? "text-orange-400"
//                                     : "text-red-400"
//                               : "text-red-400"
//                           }
//                         >
//                           {translationWebSocket
//                             ? translationWebSocket.readyState === 1
//                               ? "🟢 Connected"
//                               : translationWebSocket.readyState === 0
//                                 ? "🟡 Connecting"
//                                 : translationWebSocket.readyState === 2
//                                   ? "🟠 Closing"
//                                   : "🔴 Closed"
//                             : "❌ Not Created"}
//                         </span>
//                       </div>
//                       <div className="flex justify-between">
//                         <span className="text-gray-400">Session:</span>
//                         <span className="text-gray-300">{translationSession || "None"}</span>
//                       </div>
//                       <div className="flex justify-between">
//                         <span className="text-gray-400">Queue Status:</span>
//                         <span className="text-gray-300">
//                           {audioQueueLength === 0 ? "Empty" : `${audioQueueLength} items`}
//                         </span>
//                       </div>
//                     </div>
//                   )}

//                   {/* Translation Display */}
//                   {currentTranscription && (
//                     <div className="bg-gray-800/50 rounded-lg p-4 border-l-4 border-blue-500">
//                       <div className="text-xs text-blue-400 mb-2 font-semibold">
//                         Original ({sourceLanguage.toUpperCase()}):
//                       </div>
//                       <div className="text-sm text-white">{currentTranscription}</div>
//                     </div>
//                   )}

//                   {currentTranslation && (
//                     <div className="bg-indigo-900/30 rounded-lg p-4 border-l-4 border-indigo-400">
//                       <div className="text-xs text-indigo-400 mb-2 font-semibold">
//                         Translation ({targetLanguage.toUpperCase()}):
//                       </div>
//                       <div className="text-sm text-white">{currentTranslation}</div>
//                     </div>
//                   )}

//                   {!remoteAudioTracks.length && (
//                     <div className="flex items-center p-3 bg-yellow-500/10 border border-yellow-500/20 rounded-lg">
//                       <svg
//                         className="w-4 h-4 text-yellow-400 mr-2"
//                         fill="none"
//                         viewBox="0 0 24 24"
//                         stroke="currentColor"
//                       >
//                         <path
//                           strokeLinecap="round"
//                           strokeLinejoin="round"
//                           strokeWidth={2}
//                           d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
//                         />
//                       </svg>
//                       <span className="text-xs text-yellow-400 font-medium">Audio track required for translation</span>
//                     </div>
//                   )}
//                 </div>
//               </div>

//               {/* Enhanced Chat Section */}
//               <div className="bg-gradient-to-br from-blue-900/50 to-purple-900/50 rounded-2xl border border-blue-500/20 shadow-xl">
//                 <div className="flex items-center justify-between p-6 border-b border-blue-500/20">
//                   <h3 className="text-xl font-bold flex items-center">
//                     <svg className="w-6 h-6 mr-2 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
//                       <path
//                         strokeLinecap="round"
//                         strokeLinejoin="round"
//                         strokeWidth={2}
//                         d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.959 8.959 0 01-4.906-1.405L3 21l2.595-5.094A8.959 8.959 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z"
//                       />
//                     </svg>
//                     Chat
//                     {unreadMessages > 0 && !isChatOpen && (
//                       <span className="bg-red-500 text-white text-xs px-2 py-1 rounded-full ml-2 animate-bounce">
//                         {unreadMessages}
//                       </span>
//                     )}
//                   </h3>
//                   <button
//                     onClick={toggleChat}
//                     className="text-sm text-blue-400 hover:text-blue-300 font-medium transition-colors duration-200"
//                   >
//                     {isChatOpen ? "Hide" : "Show"}
//                   </button>
//                 </div>

//                 {/* Session ID Debug Display */}
//                 {currentStream && (
//                   <div className="p-4 bg-blue-900/30 border-b border-blue-500/20">
//                     <p className="text-xs font-bold text-blue-200 mb-1">🔍 VIEWER CHAT SESSION ID:</p>
//                     <p className="text-xs text-blue-300 break-all font-mono bg-blue-800/30 p-2 rounded">
//                       {currentStream.session_id}
//                     </p>
//                     <div className="mt-2">
//                       <input
//                         type="text"
//                         placeholder="Override session ID for testing"
//                         className="w-full text-xs p-2 bg-blue-800/50 text-blue-100 border border-blue-600/50 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
//                         onKeyDown={(e) => {
//                           if (e.key === "Enter" && e.target.value.trim()) {
//                             const newSessionId = e.target.value.trim()
//                             console.log(`🔧 VIEWER: Overriding session ID to: ${newSessionId}`)
//                             setCurrentStream((prev) => ({
//                               ...prev,
//                               session_id: newSessionId,
//                             }))
//                             e.target.value = ""
//                           }
//                         }}
//                       />
//                       <p className="text-xs text-blue-400 mt-1">Press Enter to override session ID</p>
//                     </div>
//                   </div>
//                 )}

//                 {isChatOpen && (
//                   <div className="p-6 space-y-4">
//                     {/* Chat Messages */}
//                     <div className="bg-gray-800/50 rounded-xl border border-gray-600/50 h-80 overflow-y-auto p-4 space-y-3 scrollbar-thin scrollbar-thumb-gray-600 scrollbar-track-gray-800">
//                       {isLoadingHistory ? (
//                         <div className="flex items-center justify-center h-full">
//                           <div className="text-center">
//                             <div className="w-8 h-8 border-2 border-blue-200 border-t-blue-500 rounded-full animate-spin mx-auto mb-2"></div>
//                             <span className="text-sm text-gray-400">Loading messages...</span>
//                           </div>
//                         </div>
//                       ) : chatMessages.length === 0 ? (
//                         <div className="flex items-center justify-center h-full">
//                           <div className="text-center">
//                             <svg
//                               className="w-12 h-12 text-gray-500 mx-auto mb-2"
//                               fill="none"
//                               viewBox="0 0 24 24"
//                               stroke="currentColor"
//                             >
//                               <path
//                                 strokeLinecap="round"
//                                 strokeLinejoin="round"
//                                 strokeWidth={1}
//                                 d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.959 8.959 0 01-4.906-1.405L3 21l2.595-5.094A8.959 8.959 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z"
//                               />
//                             </svg>
//                             <p className="text-sm text-gray-400">No messages yet</p>
//                             <p className="text-xs text-gray-500 mt-1">Start the conversation!</p>
//                           </div>
//                         </div>
//                       ) : (
//                         chatMessages.map((message, index) => (
//                           <div
//                             key={`${message.id || "msg"}-${index}-${message.timestamp || Date.now()}`}
//                             className="group hover:bg-gray-700/30 rounded-lg p-3 transition-all duration-200"
//                           >
//                             <div className="flex items-start justify-between">
//                               <div className="flex-1">
//                                 <div className="flex items-center space-x-2 mb-1">
//                                   <span className={`text-sm font-semibold ${getRoleColor(message.sender_role)}`}>
//                                     {message.sender_name}
//                                   </span>
//                                   <span className="text-xs bg-gray-700 px-2 py-1 rounded-full">
//                                     {getRoleBadge(message.sender_role)}
//                                   </span>
//                                 </div>
//                                 <p className="text-sm text-gray-200 leading-relaxed">{message.message}</p>
//                               </div>
//                               <span className="text-xs text-gray-400 ml-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
//                                 {formatMessageTime(message.timestamp)}
//                               </span>
//                             </div>
//                           </div>
//                         ))
//                       )}
//                     </div>

//                     {/* Chat Input */}
//                     <div className="flex space-x-3">
//                       <input
//                         type="text"
//                         value={newMessage}
//                         onChange={(e) => setNewMessage(e.target.value)}
//                         onKeyDown={handleKeyDown}
//                         placeholder="Ask a question..."
//                         className="flex-1 bg-gray-800/50 border border-gray-600/50 rounded-xl px-4 py-3 text-sm text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
//                         disabled={!socketConnected}
//                       />
//                       <button
//                         onClick={handleSendChatMessage}
//                         disabled={!newMessage.trim() || !socketConnected || isSendingMessage}
//                         className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-6 py-3 rounded-xl font-semibold hover:shadow-lg hover:shadow-blue-500/25 disabled:from-gray-600 disabled:to-gray-700 disabled:cursor-not-allowed transition-all duration-300 transform hover:scale-105 disabled:transform-none"
//                       >
//                         {isSendingMessage ? (
//                           <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
//                         ) : (
//                           <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
//                             <path
//                               strokeLinecap="round"
//                               strokeLinejoin="round"
//                               strokeWidth={2}
//                               d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"
//                             />
//                           </svg>
//                         )}
//                       </button>
//                     </div>

//                     {/* Socket Status */}
//                     <div className="flex items-center justify-between text-xs bg-gray-800/30 rounded-lg p-3">
//                       <div className="flex items-center">
//                         <span
//                           className={`w-2 h-2 rounded-full mr-2 ${socketConnected ? "bg-green-400 animate-pulse" : "bg-red-400"}`}
//                         ></span>
//                         <span className="text-gray-400">Chat {socketConnected ? "Connected" : "Disconnected"}</span>
//                       </div>
//                       <div className="flex space-x-2">
//                         <button
//                           onClick={async () => {
//                             try {
//                               const result = await checkBackendHealth()
//                               alert(`✅ Backend is healthy!\nURL: ${result.url}\nService: ${result.data.service}`)
//                             } catch (error) {
//                               alert(`❌ Backend health check failed: ${error.message}`)
//                             }
//                           }}
//                           className="bg-blue-600 hover:bg-blue-700 text-white px-2 py-1 rounded text-xs transition-colors duration-200"
//                           title="Check Backend Health"
//                         >
//                           🏥 Health
//                         </button>
//                         <button
//                           onClick={testSocketConnection}
//                           className="bg-yellow-600 hover:bg-yellow-700 text-white px-2 py-1 rounded text-xs transition-colors duration-200"
//                           title="Test Socket Connection"
//                         >
//                           🧪 Test
//                         </button>
//                         {socketConnected && (
//                           <button
//                             onClick={sendTestMessage}
//                             className="bg-green-600 hover:bg-green-700 text-white px-2 py-1 rounded text-xs transition-colors duration-200"
//                             title="Send Test Message"
//                           >
//                             📤 Test Msg
//                           </button>
//                         )}
//                       </div>
//                     </div>
//                   </div>
//                 )}
//               </div>
//             </div>
//           </div>
//         </div>
//       </div>
//     )

//   return (
//     <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
//       <div className="max-w-7xl mx-auto px-6 py-8">
//         {/* Enhanced Header */}
//         <div className="mb-12 text-center">
//           <h1 className="text-5xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent mb-4">
//             Live Streams
//           </h1>
//           <p className="text-xl text-gray-600 mb-6">Join active streaming sessions from teachers</p>

//           <div className="flex items-center justify-center space-x-8 bg-white rounded-2xl shadow-lg p-6 max-w-2xl mx-auto">
//             <div className="flex items-center">
//               <div className="w-4 h-4 bg-green-500 rounded-full mr-3 animate-pulse"></div>
//               <span className="text-lg font-semibold text-gray-700">
//                 {activeStreams.length} Active Stream{activeStreams.length !== 1 ? "s" : ""}
//               </span>
//             </div>
//             <button
//               onClick={() => trigger()}
//               className="group bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-6 py-3 rounded-xl font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-blue-500/25 flex items-center space-x-2"
//             >
//               <svg
//                 className="w-5 h-5 group-hover:rotate-180 transition-transform duration-500"
//                 fill="none"
//                 viewBox="0 0 24 24"
//                 stroke="currentColor"
//               >
//                 <path
//                   strokeLinecap="round"
//                   strokeLinejoin="round"
//                   strokeWidth={2}
//                   d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
//                 />
//               </svg>
//               <span>Refresh</span>
//             </button>
//           </div>
//         </div>

//         {activeStreams.length === 0 ? (
//           <div className="text-center py-20">
//             <div className="bg-white rounded-3xl shadow-2xl p-12 max-w-md mx-auto transform hover:scale-105 transition-transform duration-300">
//               <div className="mx-auto h-32 w-32 text-gray-300 mb-8">
//                 <svg fill="none" viewBox="0 0 24 24" stroke="currentColor" className="w-full h-full">
//                   <path
//                     strokeLinecap="round"
//                     strokeLinejoin="round"
//                     strokeWidth={1}
//                     d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
//                   />
//                 </svg>
//               </div>
//               <h3 className="text-2xl font-bold text-gray-900 mb-4">No Active Streams</h3>
//               <p className="text-gray-500 text-lg">There are currently no live streaming sessions available.</p>
//               <div className="mt-6">
//                 <button
//                   onClick={() => trigger()}
//                   className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-3 rounded-xl font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-blue-500/25"
//                 >
//                   Check Again
//                 </button>
//               </div>
//             </div>
//           </div>
//         ) : (
//           <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
//             {activeStreams.map((stream, index) => (
//               <div
//                 key={stream.session_id || index}
//                 className="group bg-white rounded-3xl shadow-xl border border-gray-100 overflow-hidden hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 hover:scale-105"
//               >
//                 {/* Stream Header */}
//                 <div className="bg-gradient-to-r from-red-500 via-pink-500 to-purple-600 p-6 relative overflow-hidden">
//                   <div className="absolute inset-0 bg-black/10"></div>
//                   <div className="relative z-10">
//                     <div className="flex items-center justify-between mb-4">
//                       <div className="flex items-center">
//                         <div className="w-4 h-4 bg-red-400 rounded-full mr-3 animate-pulse shadow-lg"></div>
//                         <span className="text-white font-bold text-lg tracking-wide">LIVE</span>
//                       </div>
//                       <div className="bg-white/20 backdrop-blur-sm text-white text-sm px-3 py-1 rounded-full font-medium">
//                         {stream.quality || "Standard"} Quality
//                       </div>
//                     </div>
//                     <div className="absolute top-4 right-4 opacity-20">
//                       <svg className="w-16 h-16 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
//                         <path
//                           strokeLinecap="round"
//                           strokeLinejoin="round"
//                           strokeWidth={1}
//                           d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
//                         />
//                       </svg>
//                     </div>
//                   </div>
//                 </div>

//                 {/* Stream Content */}
//                 <div className="p-8">
//                   <div className="space-y-6">
//                     <div>
//                       <h3 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors duration-300">
//                         Session: {stream.session_id}
//                       </h3>
//                       <p className="text-gray-600 flex items-center">
//                         <svg
//                           className="w-4 h-4 mr-2 text-blue-500"
//                           fill="none"
//                           viewBox="0 0 24 24"
//                           stroke="currentColor"
//                         >
//                           <path
//                             strokeLinecap="round"
//                             strokeLinejoin="round"
//                             strokeWidth={2}
//                             d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
//                           />
//                         </svg>
//                         Teacher ID: {stream.teacher_id}
//                       </p>
//                     </div>

//                     {/* Stats Grid */}
//                     <div className="grid grid-cols-2 gap-4">
//                       <div className="text-center p-4 bg-gradient-to-br from-blue-50 to-indigo-100 rounded-2xl border border-blue-200 group-hover:from-blue-100 group-hover:to-indigo-200 transition-all duration-300">
//                         <div className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
//                           {stream.viewer_count || 0}
//                         </div>
//                         <div className="text-sm text-gray-600 font-medium">Viewers</div>
//                       </div>
//                       <div className="text-center p-4 bg-gradient-to-br from-green-50 to-emerald-100 rounded-2xl border border-green-200 group-hover:from-green-100 group-hover:to-emerald-200 transition-all duration-300">
//                         <div className="text-3xl font-bold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
//                           {formatUptime(stream.uptime || 0)}
//                         </div>
//                         <div className="text-sm text-gray-600 font-medium">Uptime</div>
//                       </div>
//                     </div>

//                     {/* Features */}
//                     <div>
//                       <h4 className="text-sm font-bold text-gray-900 mb-3 flex items-center">
//                         <svg
//                           className="w-4 h-4 mr-2 text-purple-500"
//                           fill="none"
//                           viewBox="0 0 24 24"
//                           stroke="currentColor"
//                         >
//                           <path
//                             strokeLinecap="round"
//                             strokeLinejoin="round"
//                             strokeWidth={2}
//                             d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
//                           />
//                         </svg>
//                         Features
//                       </h4>
//                       <div className="flex flex-wrap gap-2">
//                         {stream.features?.screen_sharing && (
//                           <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-blue-100 text-blue-800 border border-blue-200">
//                             <svg className="w-3 h-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
//                               <path
//                                 strokeLinecap="round"
//                                 strokeLinejoin="round"
//                                 strokeWidth={2}
//                                 d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
//                               />
//                             </svg>
//                             Screen Sharing
//                           </span>
//                         )}
//                         {stream.features?.chat_enabled && (
//                           <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-green-100 text-green-800 border border-green-200">
//                             <svg className="w-3 h-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
//                               <path
//                                 strokeLinecap="round"
//                                 strokeLinejoin="round"
//                                 strokeWidth={2}
//                                 d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.959 8.959 0 01-4.906-1.405L3 21l2.595-5.094A8.959 8.959 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z"
//                               />
//                             </svg>
//                             Chat
//                           </span>
//                         )}
//                         {stream.features?.recording_enabled && (
//                           <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-red-100 text-red-800 border border-red-200">
//                             <svg className="w-3 h-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
//                               <path
//                                 strokeLinecap="round"
//                                 strokeLinejoin="round"
//                                 strokeWidth={2}
//                                 d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
//                               />
//                             </svg>
//                             Recording
//                           </span>
//                         )}
//                       </div>
//                     </div>

//                     <div className="text-xs text-gray-500 flex items-center">
//                       <svg className="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
//                         <path
//                           strokeLinecap="round"
//                           strokeLinejoin="round"
//                           strokeWidth={2}
//                           d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
//                         />
//                       </svg>
//                       Started: {formatDateTime(stream.created_at)}
//                     </div>
//                   </div>

//                   {/* Join Button */}
//                   <div className="mt-8">
//                     <button
//                       onClick={() => handleJoinStream(stream)}
//                       disabled={isJoining}
//                       className="group w-full bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 hover:from-blue-700 hover:via-purple-700 hover:to-indigo-700 disabled:from-gray-400 disabled:via-gray-500 disabled:to-gray-600 text-white font-bold py-4 px-6 rounded-2xl transition-all duration-500 transform hover:scale-105 hover:shadow-2xl hover:shadow-blue-500/25 disabled:transform-none disabled:shadow-none flex items-center justify-center relative overflow-hidden"
//                     >
//                       <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
//                       {isJoining ? (
//                         <div className="flex items-center relative z-10">
//                           <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-3"></div>
//                           <span className="text-lg">Joining Stream...</span>
//                         </div>
//                       ) : (
//                         <div className="flex items-center relative z-10">
//                           <svg
//                             className="w-6 h-6 mr-3 group-hover:scale-110 transition-transform duration-300"
//                             fill="none"
//                             viewBox="0 0 24 24"
//                             stroke="currentColor"
//                           >
//                             <path
//                               strokeLinecap="round"
//                               strokeLinejoin="round"
//                               strokeWidth={2}
//                               d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1.586a1 1 0 01.707.293l2.414 2.414a1 1 0 00.707.293H15M9 10V9a2 2 0 012-2h2a2 2 0 012 2v1M9 10v5a2 2 0 002 2h2a2 2 0 002-2v-5"
//                             />
//                           </svg>
//                           <span className="text-lg">Join Stream</span>
//                         </div>
//                       )}
//                     </button>
//                   </div>
//                 </div>
//               </div>
//             ))}
//           </div>
//         )}

//         {/* Enhanced Footer Info */}
//         {data && (
//           <div className="mt-16 bg-white rounded-3xl shadow-xl border border-gray-100 p-8">
//             <div className="flex items-center justify-between">
//               <div className="flex items-center space-x-6">
//                 <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center">
//                   <svg className="w-8 h-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
//                     <path
//                       strokeLinecap="round"
//                       strokeLinejoin="round"
//                       strokeWidth={2}
//                       d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
//                     />
//                   </svg>
//                 </div>
//                 <div>
//                   <h3 className="text-2xl font-bold text-gray-900">{data.service || "Streaming Service"}</h3>
//                   <p className="text-gray-600 text-lg">
//                     Total Streams Available:{" "}
//                     <span className="font-semibold text-blue-600">{data.total_count || 0}</span>
//                   </p>
//                 </div>
//               </div>
//               <div className="text-right">
//                 <div className="text-sm text-gray-500 mb-1">Last Updated</div>
//                 <div className="text-lg font-semibold text-gray-900">{formatDateTime(data.timestamp)}</div>
//               </div>
//             </div>
//           </div>
//         )}
//       </div>
//     </div>
//   )
// }

// export default CenterTraineeLiveViewer