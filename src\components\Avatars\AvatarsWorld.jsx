import React from 'react';
import { Canvas } from '@react-three/fiber';
import { OrbitControls, PerspectiveCamera, Environment } from '@react-three/drei';
import { Avatars } from './Avatars';

// Editable light intensities
const KEY_LIGHT_INTENSITY = 0.6;
const FILL_LIGHT_INTENSITY = 0.2;
const RIM_LIGHT_INTENSITY = 0.3;

const AvatarsWorld = () => {
  return (
    <Canvas
      className=""
      shadows
      gl={{ antialias: true, preserveDrawingBuffer: true }}
      dpr={[1, 2]}
      camera={{ position: [1, 0, 1], fov: 20, near: 0.1, far: 100 }}
    >
      {/* High-Quality Camera */}
      <PerspectiveCamera makeDefault position={[0, 0.1, 1]} fov={20} />

      {/* Studio Lighting Setup */}
      <directionalLight
        castShadow
        position={[0, 2, 2]}
        intensity={KEY_LIGHT_INTENSITY}
        shadow-mapSize-width={4240}
        shadow-mapSize-height={4240}
      />
      <directionalLight position={[2, 6, 5]} intensity={FILL_LIGHT_INTENSITY} color="#FDF4DC" />
      <directionalLight position={[2, 6, 5]} intensity={RIM_LIGHT_INTENSITY} color="#FDF4DC" />

      {/* Environment */}
      <Environment preset="sunset" />

      {/* Shadow Receiver */}
      <mesh receiveShadow rotation={[-Math.PI / 2, 0, 0]} position={[0, 0.2, 0]}>
        <planeGeometry args={[10, 10]} />
        <shadowMaterial transparent opacity={0.2} />
      </mesh>

      {/* Avatar */}
      <Avatars position={[0, -1.37, 0]} scale={[1.5, 1.5, 1.5]} />

      {/* Orbit Controls */}
      <OrbitControls
        enablePan={false}
        enableZoom={false}
        enableRotate={false}
        maxPolarAngle={Math.PI / 2}
      />
    </Canvas>
  );
};

export default AvatarsWorld;
