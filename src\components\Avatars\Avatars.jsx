import React, { useEffect, useRef } from 'react';
import { useGLTF, useFBX } from '@react-three/drei';
import { useFrame } from '@react-three/fiber';
import { SkeletonUtils } from 'three-stdlib';
import * as THREE from 'three';
 
export function Avatars(props) {
  const { scene } = useGLTF('/avatar_1.glb');
  const { animations: animClips } = useFBX('/idle_sasthra_female.fbx');
 
  const clone = React.useMemo(() => SkeletonUtils.clone(scene), [scene]);
  const mixer = useRef();
  const blinkTimer = useRef(0);
  const isBlinking = useRef(false);
  const blinkDuration = 0.1;
 
  const nodes = {};
  clone.traverse((o) => {
    if (o.isMesh || o.isSkinnedMesh) {
      nodes[o.name] = o;
    }
  });
 
  // ❌ Remove or hide the scalp mesh
  useEffect(() => {
    const scalp = nodes['outfit_outfit_0085_16'];
    if (scalp) {
      scalp.visible = false; // 👈 this hides it without breaking hierarchy
    }
  }, [nodes]);
 
  useEffect(() => {
    if (animClips && animClips.length > 0) {
      mixer.current = new THREE.AnimationMixer(clone);
      const action = mixer.current.clipAction(animClips[0]);
      action.play();
    }
  }, [animClips, clone]);
 
  useFrame((state, delta) => {
    mixer.current?.update(delta);
 
    const headMesh = nodes.outfit_outfit_0085_12;
    if (headMesh?.morphTargetInfluences && headMesh.morphTargetDictionary) {
      blinkTimer.current += delta;
      if (!isBlinking.current && blinkTimer.current > 3 + Math.random() * 2) {
        isBlinking.current = true;
        blinkTimer.current = 0;
      }
 
      if (isBlinking.current) {
        // If you need blinking, uncomment this and ensure the shape keys are working
        // const dict = headMesh.morphTargetDictionary;
        // const blinkProgress = blinkTimer.current / blinkDuration;
        // const blinkValue = blinkProgress < 0.5 ? blinkProgress * 2 : (1 - blinkProgress) * 2;
 
        // if (dict['Eye_Blink_L']) headMesh.morphTargetInfluences[dict['Eye_Blink_L']] = blinkValue;
        // if (dict['Eye_Blink_R']) headMesh.morphTargetInfluences[dict['Eye_Blink_R']] = blinkValue;
 
        if (blinkTimer.current > blinkDuration) {
          isBlinking.current = false;
          blinkTimer.current = 0;
        }
      }
    }
  });
 
  return <primitive object={clone} {...props} />;
}
 
useGLTF.preload('/avatar_1.glb');