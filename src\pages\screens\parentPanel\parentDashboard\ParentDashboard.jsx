"use client"

import React, { useEffect, useState } from "react"
import { motion } from "framer-motion"

const ParentDashboard = () => {
  const [parentName, setParentName] = useState(null)

  // Fetch parent name from localStorage
  useEffect(() => {
    const name = localStorage.getItem("parentName")
    setParentName(name || "Guest Parent")
  }, [])

  if (!parentName) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#10e7dc] mx-auto mb-4"></div>
          <p className="text-gray-600 font-medium">Loading...</p>
        </div>
      </div>
    )
  }

  return (
    <div
      className="min-h-screen bg-gradient-to-br from-gray-50 via-[#10e7dc]/5 to-gray-50 p-6"
      style={{ "--color-parent": "#10e7dc" }}
    >
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, ease: "easeOut" }}
        className="max-w-4xl mx-auto"
      >
        {/* Welcome Message */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4, duration: 0.6 }}
          className="bg-white/95 backdrop-blur-sm rounded-xl p-8 shadow-lg border-l-4 border-[#10e7dc]/20 text-center"
        >
          <h1 className="text-3xl font-bold text-[#10e7dc] mb-4">
            Welcome, {parentName}!
          </h1>
          <p className="text-lg text-gray-600">
            Thank you for being part of our community. Stay updated with important announcements and resources.
          </p>
          <div className="w-32 h-1 bg-[#10e7dc]/60 mx-auto rounded-full mt-6"></div>
        </motion.div>

        {/* Basic Content */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6, duration: 0.6 }}
          className="mt-8 bg-white/95 backdrop-blur-sm rounded-xl p-8 shadow-lg"
        >
          <h2 className="text-2xl font-semibold text-[#0bc5ba] mb-4">Announcements</h2>
          <p className="text-gray-600">
            Stay tuned for upcoming events and updates. Check back regularly for new information or contact our support team for assistance.
          </p>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="mt-6 px-6 py-2 rounded-full text-white text-sm font-medium bg-[#10e7dc] hover:bg-[#0bc5ba] transition-colors"
          >
            Contact Support
          </motion.button>
        </motion.div>
      </motion.div>
    </div>
  )
}

export default ParentDashboard