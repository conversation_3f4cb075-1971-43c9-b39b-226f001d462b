import React, { Suspense } from 'react';
import AvatarsWorld from '../../../../components/Avatars/AvatarsWorld';

const AiMission = () => {
  return (
    <div
      className="relative h-[20vh] w-[20vw] flex items-center justify-center overflow-hidden"
      style={{ aspectRatio: '4 / 5' }} // Enforce passport photo aspect ratio
    >
      <Suspense fallback={<div className='text-black'>Loading...</div>}>
        <AvatarsWorld />
      </Suspense>
    </div>
  );
};

export default AiMission;