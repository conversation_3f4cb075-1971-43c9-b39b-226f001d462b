import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Stethoscope,
  Rocket,
  Settings,
  Layers,
  List,
  ChevronDown,
  Loader2,
  GraduationCap,
  FileText,
  Check,
  CheckCircle,
  ChevronsUpDown,
  Download,
  AlertCircle,
  Send,
  Dna,
  FlaskConical,
  Calculator,
  Gauge
} from 'lucide-react';
import {
  useLazyCreateYourOwnTestExamNamesQuery,
  useLazyCreateYourOwnTestExamModulesQuery,
  useLazyCreateYourOwnTestExamUnitsQuery,
  useLazyCreateYourOwnTestExamSubtopicsQuery
} from '../../studentPanel/createYourOwnTest/createYourOwnTest.slice';
import Toastify from '../../../../components/PopUp/Toastify';
import { usePaperBasedBioTestStartTestMutation } from './paperBasedBioTest.slice';
import { usePaperBasedChemistryTestStartTestMutation } from './paperBasedChemistryTest.slice';
import { usePaperBasedMathTestStartTestMutation } from './paperBasedMathTest.slice';
import { usePaperBasedPhysicsTestStartTestMutation } from './paperBasedPhysicsTest.slice';
import { getSubjectApiFunction, isSubjectSupported } from './subjectApiMapping';
import LandingLoader from './LandingLoader';
import QuestionGenerationLoader from './QuestionGenerationLoader';

// 3D Question Cube Selector
function QuestionCube({ value, min, max, onChange }) {
  const [isInteracting, setIsInteracting] = useState(false);
  const rotation = ((value - min) / (max - min)) * 180; // Map value to 0° to 180°

  return (
    <div className="relative w-72 h-72 mx-auto perspective-1200">
      <motion.div
        className="absolute inset-0 flex items-center justify-center"
        animate={{
          rotateX: isInteracting ? [0, 8, -8, 0] : 0,
          scale: isInteracting ? 1.1 : 1
        }}
        transition={{ duration: 0.4 }}
        style={{ transformStyle: 'preserve-3d' }}>
        <motion.div
          className="w-40 h-40 bg-white border-4 border-[var(--color-trainee)] rounded-lg flex items-center justify-center shadow-lg shadow-[var(--color-trainee)]/40"
          animate={{ rotateY: rotation }}
          transition={{ type: 'spring', stiffness: 200, damping: 20 }}
          style={{ transformStyle: 'preserve-3d' }}>
          <span className="text-[var(--color-trainee)] font-extrabold text-6xl">{value}</span>
        </motion.div>
      </motion.div>
      <motion.div
        className="absolute top-2 ml-30 w-8 h-8 bg-white rounded-full shadow-[var(--color-trainee)]/60"
        style={{ transformOrigin: '0 36px', transformStyle: 'preserve-3d' }}
        animate={{ rotate: rotation }}
        transition={{ type: 'spring', stiffness: 200, damping: 20 }}
      />
      <input
        type="range"
        min={min}
        max={max}
        value={value}
        onChange={onChange}
        onMouseDown={() => setIsInteracting(true)}
        onTouchStart={() => setIsInteracting(true)}
        onMouseUp={() => setIsInteracting(false)}
        onTouchEnd={() => setIsInteracting(false)}
        className="absolute inset-0 opacity-0 cursor-pointer"
        aria-label="Select number of questions"
      />
      <motion.div
        className="absolute bottom-1 ml-20 mb-5 text-white text-sm font-medium"
        animate={{ opacity: isInteracting ? 0 : 1, y: isInteracting ? 8 : 0 }}
        transition={{ duration: 0.3 }}>
        Rotate to set questions
      </motion.div>
    </div>
  );
}

const steps = [
  { key: 'exam', label: 'Exam', icon: <Stethoscope size={24} /> },
  { key: 'subject', label: 'Subject', icon: <Dna size={24} /> },
  { key: 'unit', label: 'Unit', icon: <Layers size={24} /> },
  { key: 'subtopic', label: 'Subtopic', icon: <List size={24} /> },
  { key: 'generate', label: 'Generate', icon: <FileText size={24} /> }
];

const PaperBasedTest = () => {
  const [examNames, setExamNames] = useState([]);
  const [selectedExam, setSelectedExam] = useState('');
  const [subjects, setSubjects] = useState([]);
  const [selectedSubject, setSelectedSubject] = useState('');
  const [units, setUnits] = useState([]);
  const [selectedUnits, setSelectedUnits] = useState([]);
  const [subtopics, setSubtopics] = useState([]);
  const [selectedSubtopics, setSelectedSubtopics] = useState([]);
  const [toastMessage, setToastMessage] = useState(null);
  const [expandedSection, setExpandedSection] = useState('exam');
  const [selectionMode, setSelectionMode] = useState('single');
  const [showGenerateButton, setShowGenerateButton] = useState(false);
  const [questionCount, setQuestionCount] = useState(10);
  const [showQuestionCount, setShowQuestionCount] = useState(false);
  const [paperUrls, setPaperUrls] = useState([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [isLanding, setIsLanding] = useState(true);

  const userId = sessionStorage.getItem('userId');

  const [
    triggerExamNames,
    { data: examNamesData, error: examNamesError, isLoading: isLoadingExamNames }
  ] = useLazyCreateYourOwnTestExamNamesQuery();
  const [
    triggerExamModules,
    { data: subjectsData, error: subjectsError, isLoading: isLoadingSubjects }
  ] = useLazyCreateYourOwnTestExamModulesQuery();
  const [triggerExamUnits, { data: unitsData, error: unitsError, isLoading: isLoadingUnits }] =
    useLazyCreateYourOwnTestExamUnitsQuery();
  const [triggerExamSubtopics, { isLoading: isLoadingSubtopics }] =
    useLazyCreateYourOwnTestExamSubtopicsQuery();
  const [triggerBioTest, { error: bioError }] = usePaperBasedBioTestStartTestMutation();
  const [triggerChemistryTest, { error: chemistryError }] =
    usePaperBasedChemistryTestStartTestMutation();
  const [triggerMathTest, { error: mathError }] = usePaperBasedMathTestStartTestMutation();
  const [triggerPhysicsTest, { error: physicsError }] = usePaperBasedPhysicsTestStartTestMutation();

  // Landing Animation Timeout
  useEffect(() => {
    const timer = setTimeout(() => setIsLanding(false), 3500);
    return () => clearTimeout(timer);
  }, []);

  // Fetch exam names
  useEffect(() => {
    triggerExamNames();
  }, [triggerExamNames]);

  useEffect(() => {
    if (examNamesData) {
      setExamNames(examNamesData.exam_names?.map((item) => item.exam_name) || []);
    }
    if (examNamesError) {
      setToastMessage({
        type: 'error',
        message: examNamesError?.data?.error || 'Failed to fetch exam names'
      });
    }
  }, [examNamesData, examNamesError]);

  useEffect(() => {
    if (!selectedExam) {
      setSubjects([]);
      setSelectedSubject('');
      setUnits([]);
      setSelectedUnits([]);
      setSubtopics([]);
      setSelectedSubtopics([]);
      setShowGenerateButton(false);
      setShowQuestionCount(false);
      setPaperUrls([]);
      return;
    }
    triggerExamModules(selectedExam);
    setExpandedSection('subject');
  }, [selectedExam, triggerExamModules]);

  useEffect(() => {
    if (subjectsData) {
      setSubjects(subjectsData.subjects || []);
    }
    if (subjectsError) {
      console.log('Error fetching subjects:', subjectsError?.data?.error);
    }
  }, [subjectsData, subjectsError]);

  useEffect(() => {
    if (!selectedExam || !selectedSubject) {
      setUnits([]);
      setSelectedUnits([]);
      setSubtopics([]);
      setSelectedSubtopics([]);
      setShowGenerateButton(false);
      setShowQuestionCount(false);
      setPaperUrls([]);
      return;
    }
    triggerExamUnits({ examName: selectedExam, subject: selectedSubject });
    setExpandedSection('unit');
  }, [selectedExam, selectedSubject, triggerExamUnits]);

  useEffect(() => {
    if (unitsData) {
      setUnits(unitsData.units || []);
    }
    if (unitsError) {
      console.log('Error fetching units:', unitsError?.data?.error);
    }
  }, [unitsData, unitsError]);

  useEffect(() => {
    if (!selectedExam || !selectedSubject || selectedUnits.length === 0) {
      setSubtopics([]);
      setSelectedSubtopics([]);
      setShowGenerateButton(false);
      setShowQuestionCount(false);
      setPaperUrls([]);
      return;
    }
    setShowQuestionCount(true);
    const fetchSubtopics = async () => {
      try {
        const allSubtopics = [];
        for (const unit of selectedUnits) {
          const result = await triggerExamSubtopics({
            examName: selectedExam,
            module: selectedSubject,
            unit: unit
          });
          if (result.data?.subtopics) {
            allSubtopics.push(
              ...result.data.subtopics.map((st) => ({
                name: st,
                unit: unit
              }))
            );
          }
        }
        setSubtopics(allSubtopics);
      } catch (error) {
        console.log('Error fetching subtopics:', error);
      }
    };
    fetchSubtopics();
    setExpandedSection('subtopic');
  }, [selectedExam, selectedSubject, selectedUnits, triggerExamSubtopics]);

  useEffect(() => {
    const error = bioError || chemistryError || mathError || physicsError;
    if (error) {
      setToastMessage({
        type: 'error',
        message: error?.data?.error || 'Failed to generate question papers'
      });
      setIsGenerating(false);
    }
  }, [bioError, chemistryError, mathError, physicsError]);

  const toggleSelectionMode = () => {
    const newMode = selectionMode === 'single' ? 'multiple' : 'single';
    setSelectionMode(newMode);
    if (newMode === 'single') {
      setSelectedUnits((prev) => (prev.length > 0 ? [prev[0]] : []));
      setSelectedSubtopics((prev) => (prev.length > 0 ? [prev[0]] : []));
    }
    setShowGenerateButton(false);
    setPaperUrls([]);
  };

  const toggleUnitSelection = (unit) => {
    if (selectionMode === 'single') {
      setSelectedUnits([unit]);
    } else {
      setSelectedUnits((prev) =>
        prev.includes(unit) ? prev.filter((u) => u !== unit) : [...prev, unit]
      );
    }
    setSelectedSubtopics([]);
    setShowGenerateButton(false);
    setPaperUrls([]);
  };

  const toggleSelectAllUnits = () => {
    if (selectedUnits.length === units.length) {
      setSelectedUnits([]);
      setSubtopics([]);
      setSelectedSubtopics([]);
      setShowQuestionCount(false);
      setPaperUrls([]);
    } else {
      setSelectedUnits([...units]);
      setShowQuestionCount(true);
    }
    setShowGenerateButton(false);
  };

  const toggleSubtopicSelection = (subtopic) => {
    if (selectionMode === 'single') {
      setSelectedSubtopics([subtopic]);
    } else {
      setSelectedSubtopics((prev) =>
        prev.some((st) => st.name === subtopic.name && st.unit === subtopic.unit)
          ? prev.filter((st) => !(st.name === subtopic.name && st.unit === subtopic.unit))
          : [...prev, subtopic]
      );
    }
    setShowGenerateButton(true);
    setPaperUrls([]);
  };

  const toggleSelectAllSubtopics = () => {
    if (selectedSubtopics.length === subtopics.length) {
      setSelectedSubtopics([]);
      setShowGenerateButton(false);
      setPaperUrls([]);
    } else {
      setSelectedSubtopics([...subtopics]);
      setShowGenerateButton(true);
    }
  };

  const handleGenerateQuestions = async () => {
    if (!selectedExam || !selectedSubject || !selectedUnits || selectedSubtopics.length === 0) {
      setToastMessage({
        type: 'error',
        message: 'Please select an exam, subject, unit, and subtopic.'
      });
      return;
    }
    if (!isSubjectSupported(selectedSubject)) {
      setToastMessage({
        type: 'error',
        message: `Unsupported subject: ${selectedSubject}.`
      });
      return;
    }
    setIsGenerating(true);
    setPaperUrls([]);
    try {
      const papers = [];
      const apiFunctions = {
        biology: triggerBioTest,
        chemistry: triggerChemistryTest,
        physics: triggerPhysicsTest,
        math: triggerMathTest
      };
      const selectedApiFunction = getSubjectApiFunction(selectedSubject, apiFunctions);
      if (!selectedApiFunction) {
        throw new Error(`No API function for subject: ${selectedSubject}`);
      }
      const subtopicsByUnit = selectedUnits.reduce((acc, unit) => {
        acc[unit] = selectedSubtopics.filter((st) => st.unit === unit).map((st) => st.name);
        return acc;
      }, {});
      for (const unit of selectedUnits) {
        const subtopics = subtopicsByUnit[unit] || [];
        if (subtopics.length === 0) continue;
        const payload = {
          user_id: userId,
          exam_name: selectedExam,
          unit_name: unit,
          sub_topics: subtopics,
          num_questions: Math.ceil(questionCount / selectedUnits.length)
        };
        const response = await selectedApiFunction(payload).unwrap();
        if (response?.paper_pdf_s3_url) {
          papers.push({
            unit,
            paper_pdf_s3_url: response.paper_pdf_s3_url,
            omr_pdf_s3_url: response.omr_pdf_s3_url || null,
            question_paper_id: response.question_paper_id || `QP-${unit}-${Date.now()}`
          });
        }
      }
      setPaperUrls(papers);
    } catch (error) {
      setToastMessage({
        type: 'error',
        message: error?.data?.error || 'Failed to generate question papers'
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const isLoading = isLoadingExamNames || isLoadingSubjects || isLoadingUnits || isLoadingSubtopics;

  const toggleSection = (section) => {
    setExpandedSection(expandedSection === section ? null : section);
  };

  // Map subjects to specific icons
  const getSubjectIcon = (subject) => {
    const subjectIcons = {
      biology: <Dna size={28} />,
      chemistry: <FlaskConical size={28} />,
      physics: <Gauge size={28} />,
      math: <Calculator size={28} />
    };
    return subjectIcons[subject.toLowerCase()] || <Dna size={28} />;
  };

  // Map exams to specific icons for NEET, JEE Main, JEE Advanced
  const getExamIcon = (exam) => {
    if (exam.toUpperCase().includes('NEET')) {
      return <Stethoscope size={28} />;
    } else if (exam.toUpperCase().includes('JEE MAIN')) {
      return <Settings size={28} />;
    } else if (exam.toUpperCase().includes('JEE ADVANCED')) {
      return <Rocket size={28} />;
    }
    return <Stethoscope size={28} />;
  };

  return (
    <div className="min-h-screen bg-gray p-8">
      <style jsx>{`
        :root {
          --color-trainee: #f59e0b;
        }
        .perspective-1200 {
          perspective: 1200px;
        }
      `}</style>
      <Toastify res={toastMessage} resClear={() => setToastMessage(null)} />

      <AnimatePresence>{isLanding && <LandingLoader />}</AnimatePresence>

      {!isLanding && (
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 1, type: 'spring', stiffness: 120 }}
          className="max-w-4xl mx-auto perspective-1200">
          <motion.div
            className="text-center mb-12"
            initial={{ opacity: 0, y: -40, rotateX: 15 }}
            animate={{ opacity: 1, y: 0, rotateX: 0 }}
            transition={{ duration: 0.8, type: 'spring' }}
            style={{ transformStyle: 'preserve-3d' }}>
            <motion.div
              className="inline-block p-5 bg-[var(--color-trainee)]/20 rounded-xl border border-[var(--color-trainee)]/50 shadow-[var(--color-trainee)]/30"
              animate={{ rotateY: [0, 360] }}
              transition={{ duration: 10, repeat: Infinity, ease: 'linear' }}>
              < GraduationCap size={48} className="text-[var(--color-trainee)]" />
            </motion.div>
            <h1 className="text-4xl font-extrabold text-[var(--color-trainee)] mt-4 font-poppins tracking-tight">
              NEET & JEE Test Builder
            </h1>
            <p className="text-black mt-2 text-lg font-poppins">
              Create tailored practice tests for JEE Main,NEET, JEE Advanced.
            </p>
          </motion.div>

          {selectedExam && (
            <motion.div
              className="flex justify-between mb-12"
              initial={{ opacity: 0, rotateX: 20 }}
              animate={{ opacity: 1, rotateX: 0 }}
              transition={{ duration: 0.6 }}
              style={{ transformStyle: 'preserve-3d' }}>
              {steps.map((step, idx) => {
                const isActive =
                  expandedSection === step.key || (step.key === 'generate' && showGenerateButton);
                const isCompleted =
                  paperUrls.length > 0 ||
                  (idx < 4 && selectedSubtopics.length > 0) ||
                  (idx < 3 && subtopics.length > 0) ||
                  (idx < 2 && selectedUnits.length > 0) ||
                  (idx < 1 && selectedSubject) ||
                  (idx < 0 && selectedExam);
                return (
                  <motion.div
                    key={step.key}
                    className="flex flex-col items-center relative"
                    initial={{ opacity: 0, y: 20, rotateX: 15 }}
                    animate={{ opacity: 1, y: 0, rotateX: 0 }}
                    transition={{ delay: idx * 0.2 }}
                    style={{ transformStyle: 'preserve-3d' }}>
                    <motion.div
                      className={`w-12 h-12 rounded-full flex items-center justify-center border-2 shadow-[var(--color-trainee)]/40 ${
                        isActive
                          ? 'bg-[var(--color-trainee)] border-[var(--color-trainee)] text-white'
                          : isCompleted
                            ? 'bg-white/20 border-[var(--color-trainee)] text-[var(--color-trainee)]'
                            : 'bg-white/10 border-white/30 text-[var(--color-trainee)]'
                      }`}
                      animate={{
                        scale: isActive ? [1, 1.1, 1] : 1,
                        rotateX: isActive ? [0, 8, -8, 0] : 0
                      }}
                      transition={{ duration: 1, repeat: isActive ? Infinity : 0 }}
                      style={{ transformStyle: 'preserve-3d' }}>
                      {isCompleted && !isActive ? <Check size={24} /> : step.icon}
                    </motion.div>
                    <span className="text-sm font-bold text-black mt-2 ">{step.label}</span>
                    {idx < steps.length - 1 && (
                      <motion.div
                        className="absolute top-6 left-12 right-[-48px] h-1 bg-[var(--color-trainee)]/50 rounded-full"
                        animate={{ scaleX: isCompleted ? 1 : 0 }}
                        transition={{ duration: 0.5 }}
                        style={{ transformStyle: 'preserve-3d' }}
                      />
                    )}
                  </motion.div>
                );
              })}
            </motion.div>
          )}

          {isLoading && (
            <motion.div
              className="flex justify-center py-8"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5 }}>
              <Loader2 className="animate-spin text-[var(--color-trainee)]" size={36} />
              <motion.span
                className="ml-3 text-white text-lg font-poppins"
                animate={{ opacity: [0.5, 1, 0.5] }}
                transition={{ duration: 1.2, repeat: Infinity }}>
                Loading...
              </motion.span>
            </motion.div>
          )}

          <motion.div
            className="flex justify-end mb-8"
            initial={{ opacity: 0, x: 20, rotateX: 15 }}
            animate={{ opacity: 1, x: 0, rotateX: 0 }}
            transition={{ duration: 0.5 }}
            style={{ transformStyle: 'preserve-3d' }}>
            <motion.button
              onClick={toggleSelectionMode}
              className="flex items-center gap-2 px-5 py-2 bg-[var(--color-trainee)] hover:cursor-pointer border-2 border-[var(--color-trainee)] text-white rounded-lg shadow-[var(--color-trainee)]/30 font-poppins"
              whileHover={{ scale: 1.05, rotateX: 8, rotateY: 8 }}
              whileTap={{ scale: 0.95 }}
              style={{ transformStyle: 'preserve-3d' }}
              aria-label={`Switch to ${selectionMode === 'single' ? 'multiple' : 'single'} selection mode`}>
              <ChevronsUpDown size={20} />
              {selectionMode === 'single' ? 'Single' : 'Multiple'} Mode
            </motion.button>
          </motion.div>

          <motion.div
            className="mb-8 bg-[var(--color-trainee)] rounded-xl p-6 border border-[var(--color-trainee)]/50 backdrop-blur-sm shadow-[var(--color-trainee)]/20"
            initial={{ opacity: 0, y: 20, rotateX: 10 }}
            animate={{ opacity: 1, y: 0, rotateX: 0 }}
            transition={{ duration: 0.6 }}
            style={{ transformStyle: 'preserve-3d' }}>
            <button
              onClick={() => toggleSection('exam')}
              className="w-full flex justify-between items-center text-white"
              style={{ transformStyle: 'preserve-3d' }}
              aria-expanded={expandedSection === 'exam'}
              aria-controls="exam-section">
              <div className="flex items-center gap-3">
                <motion.div
                  className="p-2 bg-[var(--color-trainee)]/30 rounded-lg"
                  animate={{ rotateY: expandedSection === 'exam' ? [0, 10, -10, 0] : 0 }}
                  transition={{ duration: 0.5 }}>
                  <Stethoscope size={28} className="text-white" />
                </motion.div>
                <span className="font-bold text-lg text-white font-poppins">
                  {selectedExam || 'Select Exam'}
                </span>
              </div>
              <motion.div
                animate={{ rotate: expandedSection === 'exam' ? 180 : 0 }}
                className="p-2 bg-white rounded-full">
                <ChevronDown size={20} className="text-[var(--color-trainee)]" />
              </motion.div>
            </button>
            <AnimatePresence>
              {expandedSection === 'exam' && (
                <motion.div
                  id="exam-section"
                  initial={{ height: 0, opacity: 0, rotateX: -15 }}
                  animate={{ height: 'auto', opacity: 1, rotateX: 0 }}
                  exit={{ height: 0, opacity: 0, rotateX: -15 }}
                  transition={{ duration: 0.4 }}
                  className="mt-4 space-y-3"
                  style={{ transformStyle: 'preserve-3d' }}>
                  {examNames.map((exam, index) => (
                    <motion.div
                      key={exam}
                      initial={{ opacity: 0, y: 15, rotateX: 15 }}
                      animate={{ opacity: 1, y: index * 8, rotateX: 0 }}
                      transition={{ duration: 0.5, delay: index * 0.1 }}
                      className={`p-3 rounded-lg bg-white text-black border border-[var(--color-trainee)]/30 cursor-pointer backdrop-blur-sm shadow-[var(--color-trainee)]/20 ${
                        selectedExam === exam ? 'border-[var(--color-trainee)]' : ''
                      }`}
                      onClick={() => setSelectedExam(exam)}
                      whileHover={{ rotateX: 8, rotateY: 8, scale: 1.05, z: 40 }}
                      whileTap={{ scale: 0.95 }}
                      style={{ transformStyle: 'preserve-3d' }}
                      role="option"
                      aria-selected={selectedExam === exam}>
                      <div className="flex items-center gap-3">
                        <motion.div
                          className={`p-2 rounded-lg ${
                            selectedExam === exam
                              ? 'bg-[var(--color-trainee)] text-white'
                              : 'bg-white/20 text-[var(--color-trainee)]'
                          }`}
                          animate={{ scale: selectedExam === exam ? [1, 1.1, 1] : 1 }}
                          transition={{
                            duration: 0.8,
                            repeat: selectedExam === exam ? Infinity : 0
                          }}>
                          {getExamIcon(exam)}
                        </motion.div>
                        <span className=" font-medium font-poppins">{exam}</span>
                        {selectedExam === exam && (
                          <motion.div
                            initial={{ scale: 0 }}
                            animate={{ scale: 1 }}
                            className="ml-auto p-1 bg-[var(--color-trainee)] rounded-full">
                            <Check size={16} className="text-white" />
                          </motion.div>
                        )}
                      </div>
                    </motion.div>
                  ))}
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>

          {subjects.length > 0 && (
            <motion.div
              className="mb-8 bg-[var(--color-trainee)] rounded-xl p-6 border border-[var(--color-trainee)]/50 backdrop-blur-sm shadow-[var(--color-trainee)]/20"
              initial={{ opacity: 0, y: 20, rotateX: 10 }}
              animate={{ opacity: 1, y: 0, rotateX: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              style={{ transformStyle: 'preserve-3d' }}>
              <button
                onClick={() => toggleSection('subject')}
                className="w-full flex justify-between items-center text-white"
                style={{ transformStyle: 'preserve-3d' }}
                aria-expanded={expandedSection === 'subject'}
                aria-controls="subject-section">
                <div className="flex items-center gap-3">
                  <motion.div
                    className="p-2 bg-white rounded-lg"
                    animate={{ rotateY: expandedSection === 'subject' ? [0, 10, -10, 0] : 0 }}
                    transition={{ duration: 0.5 }}>
                    <Dna size={28} className="text-[var(--color-trainee)]" />
                  </motion.div>
                  <span className="font-bold text-lg text-white font-poppins">
                    {selectedSubject || 'Select Subject'}
                  </span>
                </div>
                <motion.div
                  animate={{ rotate: expandedSection === 'subject' ? 180 : 0 }}
                  className="p-2 bg-white rounded-full">
                  <ChevronDown size={20} className="text-[var(--color-trainee)]" />
                </motion.div>
              </button>
              <AnimatePresence>
                {expandedSection === 'subject' && (
                  <motion.div
                    id="subject-section"
                    initial={{ height: 0, opacity: 0, rotateX: -15 }}
                    animate={{ height: 'auto', opacity: 1, rotateX: 0 }}
                    exit={{ height: 0, opacity: 0, rotateX: -15 }}
                    transition={{ duration: 0.4 }}
                    className="mt-4 space-y-3"
                    style={{ transformStyle: 'preserve-3d' }}>
                    {subjects.map((subject, index) => (
                      <motion.div
                        key={subject}
                        initial={{ opacity: 0, y: 15, rotateX: 15 }}
                        animate={{ opacity: 1, y: index * 8, rotateX: 0 }}
                        transition={{ duration: 0.5, delay: index * 0.1 }}
                        className={`p-3 rounded-lg bg-white border border-[var(--color-trainee)]/30 cursor-pointer backdrop-blur-sm shadow-[var(--color-trainee)]/20 ${
                          selectedSubject === subject ? 'border-[var(--color-trainee)]' : ''
                        }`}
                        onClick={() => setSelectedSubject(subject)}
                        whileHover={{ rotateX: 8, rotateY: 8, scale: 1.05, z: 40 }}
                        whileTap={{ scale: 0.95 }}
                        style={{ transformStyle: 'preserve-3d' }}
                        role="option"
                        aria-selected={selectedSubject === subject}>
                        <div className="flex items-center  gap-3">
                          <motion.div
                            className={`p-2 rounded-lg ${
                              selectedSubject === subject
                                ? 'bg-[var(--color-trainee)] text-white'
                                : 'bg-white/20 text-[var(--color-trainee)]'
                            }`}
                            animate={{ scale: selectedSubject === subject ? [1, 1.1, 1] : 1 }}
                            transition={{
                              duration: 0.8,
                              repeat: selectedSubject === subject ? Infinity : 0
                            }}>
                            {getSubjectIcon(subject)}
                          </motion.div>
                          <span className=" font-medium font-poppins">{subject}</span>
                          {selectedSubject === subject && (
                            <motion.div
                              initial={{ scale: 0 }}
                              animate={{ scale: 1 }}
                              className="ml-auto p-1 bg-[var(--color-trainee)] rounded-full">
                              <Check size={16} className="text-white" />
                            </motion.div>
                          )}
                        </div>
                      </motion.div>
                    ))}
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.div>
          )}

          {units.length > 0 && (
            <motion.div
              className="mb-8  bg-[var(--color-trainee)]  rounded-xl  p-6 border border-[var(--color-trainee)]/50 backdrop-blur-sm shadow-[var(--color-trainee)]/20"
              initial={{ opacity: 0, y: 20, rotateX: 10 }}
              animate={{ opacity: 1, y: 0, rotateX: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              style={{ transformStyle: 'preserve-3d' }}>
              <button
                onClick={() => toggleSection('unit')}
                className="w-full flex justify-between items-center text-white"
                style={{ transformStyle: 'preserve-3d' }}
                aria-expanded={expandedSection === 'unit'}
                aria-controls="unit-section">
                <div className="flex items-center gap-3">
                  <motion.div
                    className="p-2 bg-[var(--color-trainee)]/30 rounded-lg"
                    animate={{ rotateY: expandedSection === 'unit' ? [0, 10, -10, 0] : 0 }}
                    transition={{ duration: 0.5 }}>
                    <Layers size={28} className="text-white" />
                  </motion.div>
                  <span className="font-bold text-lg text-white font-poppins">
                    {selectedUnits.length > 0
                      ? `${selectedUnits.length} Unit${selectedUnits.length > 1 ? 's' : ''} Selected`
                      : 'Select Unit'}
                  </span>
                </div>
                <motion.div
                  animate={{ rotate: expandedSection === 'unit' ? 90 : 0 }}
                  className="p-2 bg-white rounded-full">
                  <ChevronDown size={20} className="text-[var(--color-trainee)]" />
                </motion.div>
              </button>
              <AnimatePresence>
                {expandedSection === 'unit' && (
                  <motion.div
                    id="unit-section"
                    initial={{ height: 0, opacity: 0, rotateX: -15 }}
                    animate={{ height: 'auto', opacity: 1, rotateX: 0 }}
                    exit={{ height: 0, opacity: 0, rotateX: -15 }}
                    transition={{ duration: 0.4 }}
                    className="mt-4 space-y-3"
                    style={{ transformStyle: 'preserve-3d' }}>
                    {selectionMode === 'multiple' && (
                      <motion.button
                        onClick={toggleSelectAllUnits}
                        className="flex items-center gap-2 p-3 rounded-lg bg-white/10 border border-[var(--color-trainee)]/30 text-white hover:bg-[var(--color-trainee)]/20"
                        whileHover={{ scale: 1.05, rotateX: 8, rotateY: 8 }}
                        whileTap={{ scale: 0.95 }}
                        style={{ transformStyle: 'preserve-3d' }}
                        aria-label={
                          selectedUnits.length === units.length
                            ? 'Deselect all units'
                            : 'Select all units'
                        }>
                        <CheckCircle size={20} className="white" />
                        <span className="font-poppins">
                          {selectedUnits.length === units.length
                            ? 'Deselect All'
                            : 'Select All Units'}
                        </span>
                      </motion.button>
                    )}
                    {units.map((unit, index) => (
                      <motion.div
                        key={unit}
                        initial={{ opacity: 0, y: 15, rotateX: 15 }}
                        animate={{ opacity: 1, y: index * 8, rotateX: 0 }}
                        transition={{ duration: 0.5, delay: index * 0.1 }}
                        className={`p-3 rounded-lg bg-white border border-[var(--color-trainee)]/30 cursor-pointer backdrop-blur-sm shadow-[var(--color-trainee)]/20 ${
                          selectedUnits.includes(unit) ? 'border-[var(--color-trainee)]' : ''
                        }`}
                        onClick={() => toggleUnitSelection(unit)}
                        whileHover={{ rotateX: 8, rotateY: 8, scale: 1.05, z: 40 }}
                        whileTap={{ scale: 0.95 }}
                        style={{ transformStyle: 'preserve-3d' }}
                        role="option"
                        aria-selected={selectedUnits.includes(unit)}>
                        <div className="flex items-center gap-3">
                          <motion.div
                            className={`p-2 rounded-lg ${
                              selectedUnits.includes(unit)
                                ? 'bg-[var(--color-trainee)] text-white'
                                : 'bg-white/20 text-[var(--color-trainee)]'
                            }`}
                            animate={{ scale: selectedUnits.includes(unit) ? [1, 1.1, 1] : 1 }}
                            transition={{
                              duration: 0.8,
                              repeat: selectedUnits.includes(unit) ? Infinity : 0
                            }}>
                            <Layers size={24} />
                          </motion.div>
                          <span className=" font-medium font-poppins">{unit}</span>
                          {selectedUnits.includes(unit) && (
                            <motion.div
                              initial={{ scale: 0 }}
                              animate={{ scale: 1 }}
                              className="ml-auto p-1 bg-[var(--color-trainee)] rounded-full">
                              <Check size={16} className="text-white" />
                            </motion.div>
                          )}
                        </div>
                      </motion.div>
                    ))}
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.div>
          )}

          {subtopics.length > 0 && (
            <motion.div
              className="mb-20 bg-[var(--color-trainee)]  rounded-xl p-6 border border-[var(--color-trainee)]/50 backdrop-blur-sm shadow-[var(--color-trainee)]/20"
              initial={{ opacity: 0, y: 20, rotateX: 10 }}
              animate={{ opacity: 1, y: 0, rotateX: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              style={{ transformStyle: 'preserve-3d' }}>
              <button
                onClick={() => toggleSection('subtopic')}
                className="w-full flex justify-between items-center  text-white"
                style={{ transformStyle: 'preserve-3d' }}
                aria-expanded={expandedSection === 'subtopic'}
                aria-controls="subtopic-section">
                <div className="flex items-center gap-3">
                  <motion.div
                    className="p-2 bg-[var(--color-trainee)]/30 rounded-lg"
                    animate={{ rotateY: expandedSection === 'subtopic' ? [0, 10, -10, 0] : 0 }}
                    transition={{ duration: 0.5 }}>
                    <List size={28} className="text-white" />
                  </motion.div>
                  <span className="font-bold text-lg text-white font-poppins">
                    {selectedSubtopics.length > 0
                      ? `${selectedSubtopics.length} Subtopic${selectedSubtopics.length > 1 ? 's' : ''} Selected`
                      : 'Select Subtopic'}
                  </span>
                </div>
                <motion.div
                  animate={{ rotate: expandedSection === 'subtopic' ? 180 : 0 }}
                  className="p-2 bg-white rounded-full">
                  <ChevronDown size={20} className="text-[var(--color-trainee)]" />
                </motion.div>
              </button>
              <AnimatePresence>
                {expandedSection === 'subtopic' && (
                  <motion.div
                    id="subtopic-section"
                    initial={{ height: 0, opacity: 0, rotateX: -15 }}
                    animate={{ height: 'auto', opacity: 1, rotateX: 0 }}
                    exit={{ height: 0, opacity: 0, rotateX: -15 }}
                    transition={{ duration: 0.4 }}
                    className="mt-4 space-y-3"
                    style={{ transformStyle: 'preserve-3d' }}>
                    {selectionMode === 'multiple' && (
                      <motion.button
                        onClick={toggleSelectAllSubtopics}
                        className="flex items-center gap-2 p-3 rounded-lg bg-white/10 border border-[var(--color-trainee)]/30 text-white hover:bg-[var(--color-trainee)]/20"
                        whileHover={{ scale: 1.05, rotateX: 8, rotateY: 8 }}
                        whileTap={{ scale: 0.95 }}
                        style={{ transformStyle: 'preserve-3d' }}
                        aria-label={
                          selectedSubtopics.length === subtopics.length
                            ? 'Deselect all subtopics'
                            : 'Select all subtopics'
                        }>
                        <CheckCircle size={20} className="text-white" />
                        <span className="font-poppins">
                          {selectedSubtopics.length === subtopics.length
                            ? 'Deselect All'
                            : 'Select All Subtopics'}
                        </span>
                      </motion.button>
                    )}
                    {subtopics.map((subtopic, index) => (
                      <motion.div
                        key={`${subtopic.unit}-${subtopic.name}`}
                        initial={{ opacity: 0, y: 15, rotateX: 15 }}
                        animate={{ opacity: 1, y: index * 8, rotateX: 0 }}
                        transition={{ duration: 0.5, delay: index * 0.1 }}
                        className={`p-3 rounded-lg bg-white border border-[var(--color-trainee)]/30 cursor-pointer backdrop-blur-sm shadow-[var(--color-trainee)]/20 ${
                          selectedSubtopics.some(
                            (st) => st.name === subtopic.name && st.unit === subtopic.unit
                          )
                            ? 'border-[var(--color-trainee)]'
                            : ''
                        }`}
                        onClick={() => toggleSubtopicSelection(subtopic)}
                        whileHover={{ rotateX: 8, rotateY: 8, scale: 1.05, z: 40 }}
                        whileTap={{ scale: 0.95 }}
                        style={{ transformStyle: 'preserve-3d' }}
                        role="option"
                        aria-selected={selectedSubtopics.some(
                          (st) => st.name === subtopic.name && st.unit === subtopic.unit
                        )}>
                        <div className="flex items-center gap-3">
                          <motion.div
                            className={`p-2 rounded-lg ${
                              selectedSubtopics.some(
                                (st) => st.name === subtopic.name && st.unit === subtopic.unit
                              )
                                ? 'bg-[var(--color-trainee)] text-white'
                                : 'bg-white/20 text-[var(--color-trainee)]'
                            }`}
                            animate={{
                              scale: selectedSubtopics.some(
                                (st) => st.name === subtopic.name && st.unit === subtopic.unit
                              )
                                ? [1, 1.1, 1]
                                : 1
                            }}
                            transition={{
                              duration: 0.8,
                              repeat: selectedSubtopics.some(
                                (st) => st.name === subtopic.name && st.unit === subtopic.unit
                              )
                                ? Infinity
                                : 0
                            }}>
                            <List size={24} />
                          </motion.div>
                          <div>
                            <span className=" font-medium font-poppins">{subtopic.name}</span>
                            <p className="text-sm font-poppins">Unit: {subtopic.unit}</p>
                          </div>
                          {selectedSubtopics.some(
                            (st) => st.name === subtopic.name && st.unit === subtopic.unit
                          ) && (
                            <motion.div
                              initial={{ scale: 0 }}
                              animate={{ scale: 1 }}
                              className="ml-auto p-1 bg-[var(--color-trainee)] rounded-full">
                              <Check size={16} className="text-white" />
                            </motion.div>
                          )}
                        </div>
                      </motion.div>
                    ))}
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.div>
          )}
          {showQuestionCount && (
            <motion.div
              className="my-12 flex bg-[var(--color-trainee)] border-none rounded-3xl justify-center"
              initial={{ opacity: 0, y: 20, rotateX: 10 }}
              animate={{ opacity: 1, y: 0, rotateX: 0 }}
              transition={{ duration: 0.6 }}
              style={{ transformStyle: 'preserve-3d' }}>
              <QuestionCube
                value={questionCount}
                min={1}
                max={50}
                onChange={(e) => setQuestionCount(parseInt(e.target.value))}
              />
            </motion.div>
          )}
          {showGenerateButton && (
            <motion.div
              className="mt-12 flex justify-center"
            >
              <motion.button
                onClick={handleGenerateQuestions}
                disabled={isGenerating}
                className={`flex items-center hover:cursor-pointer gap-3 px-8 py-3 bg-[var(--color-trainee)] text-white rounded-lg shadow-[var(--color-trainee)]/40 font-poppins ${
                  isGenerating ? 'opacity-50 cursor-not-allowed' : ''
                }`}
               
            
                
             
                aria-label={
                  isGenerating ? 'Generating questions' : `Generate ${questionCount} questions`
                }>
                <Send size={20} />
                {isGenerating ? 'Generating...' : `Generate ${questionCount} Questions`}
              </motion.button>
            </motion.div>
          )}

          <AnimatePresence>
            {paperUrls.length > 0 && (
              <motion.div
                className="mt-12 bg-white/10 rounded-xl p-6 border border-[var(--color-trainee)]/50 backdrop-blur-sm shadow-[var(--color-trainee)]/20"
                initial={{ opacity: 0, y: 20, rotateX: 10 }}
                animate={{ opacity: 1, y: 0, rotateX: 0 }}
                transition={{ duration: 0.6 }}
                style={{ transformStyle: 'preserve-3d' }}>
                <motion.div
                  className="flex items-center gap-3 mb-4"
                  initial={{ opacity: 0, x: -20, rotateX: 10 }}
                  animate={{ opacity: 1, x: 0, rotateX: 0 }}
                  transition={{ duration: 0.5 }}
                  style={{ transformStyle: 'preserve-3d' }}>
                  <FileText size={28} className="text-[var(--color-trainee)]" />
                  <h2 className="text-xl font-semibold text-[var(--color-trainee)] font-poppins">
                    Your Question Papers
                  </h2>
                </motion.div>
                <motion.div className="space-y-3">
                  {paperUrls.map((paper, index) => (
                    <motion.div
                      key={paper.question_paper_id}
                      initial={{ opacity: 0, y: 15, rotateX: 15 }}
                      animate={{ opacity: 1, y: index * 8, rotateX: 0 }}
                      transition={{ duration: 0.5, delay: index * 0.1 }}
                      className="p-4 rounded-lg bg-white/10 border border-[var(--color-trainee)]/30 backdrop-blur-sm shadow-[var(--color-trainee)]/20"
                   
                      style={{ transformStyle: 'preserve-3d' }}>
                      <div className="flex justify-between items-center mb-2">
                        <h3 className="text-[var(--color-trainee)] font-medium font-poppins">Unit: {paper.unit}</h3>
                        <span className="text-white/70 text-sm font-poppins">
                          {selectedSubject}
                        </span>
                      </div>
                      <div className="flex gap-3">
                        {paper.paper_pdf_s3_url && (
                          <motion.a
                            href={paper.paper_pdf_s3_url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="flex items-center gap-2 px-4 py-2 bg-[var(--color-trainee)] text-white rounded-lg shadow-[var(--color-trainee)]/30"
                         whileHover={{ scale: 1.05, rotateX: 8 }}
                            whileTap={{ scale: 0.95 }}
                            style={{ transformStyle: 'preserve-3d' }}
                            aria-label="Download question paper">
                            <Download size={16} />
                            Question Paper
                          </motion.a>
                        )}
                        {paper.omr_pdf_s3_url && (
                          <motion.a
                            href={paper.omr_pdf_s3_url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="flex items-center gap-2 px-4 py-2 bg-[var(--color-trainee)] text-white rounded-lg shadow-[var(--color-trainee)]/30"
                            whileHover={{ scale: 1.05, rotateX: 8 }}
                            whileTap={{ scale: 0.95 }}
                            style={{ transformStyle: 'preserve-3d' }}
                            aria-label="Download OMR sheet">
                            <Download size={16} />
                            OMR Sheet
                          </motion.a>
                        )}
                      </div>
                      {!paper.paper_pdf_s3_url && (
                        <motion.p
                          className="mt-2 text-red-400 text-sm font-poppins"
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          transition={{ duration: 0.3 }}>
                          Question Paper not available
                        </motion.p>
                      )}
                      {!paper.omr_pdf_s3_url && (
                        <motion.p
                          className="mt-2 text-red-400 text-sm font-poppins"
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          transition={{ duration: 0.3 }}>
                          OMR Sheet not available
                        </motion.p>
                      )}
                    </motion.div>
                  ))}
                </motion.div>
              </motion.div>
            )}
          </AnimatePresence>

          {!isGenerating && paperUrls.length === 0 && showGenerateButton && (
            <motion.div
              className="mt-12 flex flex-col items-center text-white"
              initial={{ opacity: 0, scale: 0.8, rotateX: 10 }}
              animate={{ opacity: 1, scale: 1, rotateX: 0 }}
              transition={{ duration: 0.5 }}
              style={{ transformStyle: 'preserve-3d' }}>
              <motion.div
                animate={{ scale: [1, 1.1, 1], rotateX: [0, 8, -8, 0] }}
                transition={{ duration: 1.2, repeat: Infinity }}
                style={{ transformStyle: 'preserve-3d' }}>
                <AlertCircle size={40} className="text-[var(--color-trainee)]" />
              </motion.div>
              <motion.p
                className="mt-2 text-center text-lg font-poppins"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4 }}>
                No papers generated yet. Click "Generate Questions" to begin.
              </motion.p>
            </motion.div>
          )}
        </motion.div>
      )}

      <AnimatePresence>{isGenerating && <QuestionGenerationLoader />}</AnimatePresence>
    </div>
  );
};

export default PaperBasedTest;
