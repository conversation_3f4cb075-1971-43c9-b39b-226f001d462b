import React from 'react';
import { Navigate, Outlet } from 'react-router';

const ProtectedRoute = ({ allowedRoles }) => {
  const role = sessionStorage.getItem('role');

  if (!role) {
    return <Navigate to="/login" replace />;
  }

  // Check whether role is allowed
  if (!allowedRoles.includes(role)) {
    return <Navigate to="/login" replace />;
  }

  return <Outlet />;
};

export default ProtectedRoute;
