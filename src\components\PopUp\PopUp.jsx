import { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
// import { FiInfo } from "react-icons/fi";
import Button from '../Field/Button';
import { motion, AnimatePresence } from 'framer-motion';
import { Info } from 'lucide-react';

const PopUp = ({
  title,
  width = 'md',
  children,
  isDelete = false,
  isEdit = false,
  isDisabled = false,
  isScrollable = false,
  post = () => {},
  onClose
}) => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(true);
  }, []);

  const handleClose = () => {
    setIsVisible(false);
    setTimeout(onClose, 300);
  };

  const widthClasses = {
    sm: 'w-[40vw]',
    md: 'w-[50vw]',
    lg: 'w-[60vw]',
    xl: 'w-[70vw]'
  };

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black/60 backdrop-blur-md flex items-center justify-center z-40 transition-opacity duration-300"
        >
          <motion.div
            initial={{ scale: 0.9 }}
            animate={{ scale: 1 }}
            exit={{ scale: 0.9 }}
            transition={{ duration: 0.3 }}
            className={`bg-white rounded-lg shadow-xl max-h-[100vh] transform transition-transform duration-300 ${widthClasses[width]}`}
            onClick={(e) => e.stopPropagation()}
          >
            <div className="bg-[#7c0201] p-4 rounded-t-lg flex items-center gap-2 text-white border-b border-white/10">
              <Info size={28} />
              <h2 className="text-xl sm:text-2xl font-semibold">{title}</h2>
            </div>

            <div
              className={`p-6 ${isScrollable ? 'max-h-[70vh] overflow-y-auto' : 'overflow-visible'}`}
            >
              {children}
            </div>

            <div className="flex justify-end items-center gap-4 p-2 bg-gray-50 rounded-b-lg border-t border-gray-200">
              <Button
                name={isDelete ? 'Delete' : isEdit ? 'Update' : 'Save'}
                className={`px-5 py-2 text-sm font-medium rounded-md shadow-sm transition-colors ${
                  isDisabled
                    ? 'cursor-not-allowed bg-gray-300 text-gray-500'
                    : 'bg-green-600 hover:bg-green-700 text-white'
                }`}
                onClick={post}
                disabled={isDisabled}
              />
              <Button
                name="Close"
                className="px-5 py-2 text-sm font-medium rounded-md shadow-sm transition-colors bg-red-600 hover:bg-red-700 text-white"
                onClick={handleClose}
              />
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

PopUp.propTypes = {
  title: PropTypes.string.isRequired,
  width: PropTypes.oneOf(['sm', 'md', 'lg', 'xl']),
  children: PropTypes.node.isRequired,
  isDelete: PropTypes.bool,
  isEdit: PropTypes.bool,
  isDisabled: PropTypes.bool,
  isScrollable: PropTypes.bool,
  post: PropTypes.func,
  onClose: PropTypes.func.isRequired
};

export default PopUp;
