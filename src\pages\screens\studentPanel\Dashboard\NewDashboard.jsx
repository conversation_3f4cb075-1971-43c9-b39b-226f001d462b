import React, { useEffect } from 'react';
import {
  setAccuracyBySubject,
  setAiTutorFeedback,
  setAllQuestionsLog,
  setAttemptBreakdown,
  setConfusedAttempts,
  setErrorTopics,
  setLearningEfficiency,
  setQualityOfTime,
  setQuestionAttemptStatusTimeline,
  setScoreOverview,
  setSubjectSwitchTimeline,
  setSubjectWiseScore,
  setTimeAccuracyTrends,
  setTimeJourney,
  setTimeSpentDistribution,
  useLazyGetAccuracyBySubjectServiceQuery,
  useLazyGetAiTutorFeedbackServiceQuery,
  useLazyGetAllQuestionsLogServiceQuery,
  useLazyGetAttemptBreakdownServiceQuery,
  useLazyGetConfusedAttemptsServiceQuery,
  useLazyGetErrorTopicsServiceQuery,
  useLazyGetLearningEfficiencyServiceQuery,
  useLazyGetQualityOfTimeServiceQuery,
  useLazyGetQuestionAttemptStatusTimelineServiceQuery,
  useLazyGetScoreOverviewServiceQuery,
  useLazyGetSubjectSwitchTimelineServiceQuery,
  useLazyGetSubjectWiseScoreServiceQuery,
  useLazyGetTimeAccuracyTrendsServiceQuery,
  useLazyGetTimeJourneyServiceQuery,
  useLazyGetTimeSpentDistributionServiceQuery
} from './dashboard.slice';
import { useDispatch, useSelector } from 'react-redux';
import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';

const NewDashboard = () => {
  const [getScoreOverview] = useLazyGetScoreOverviewServiceQuery();
  const [getSubjectWiseScore] = useLazyGetSubjectWiseScoreServiceQuery();
  const [getAttemptBreakdown] = useLazyGetAttemptBreakdownServiceQuery();
  const [getAccuracyBySubject] = useLazyGetAccuracyBySubjectServiceQuery();
  const [getTimeAccuracyTrend] = useLazyGetTimeAccuracyTrendsServiceQuery();
  const [getTimeSpentDistribution] = useLazyGetTimeSpentDistributionServiceQuery();
  const [getTimeJourney] = useLazyGetTimeJourneyServiceQuery();
  const [getQualityOfTime] = useLazyGetQualityOfTimeServiceQuery();
  const [getLearningEfficiency] = useLazyGetLearningEfficiencyServiceQuery();
  const [getConfusedAttempts] = useLazyGetConfusedAttemptsServiceQuery();
  const [getSubjectSwitchTimeline] = useLazyGetSubjectSwitchTimelineServiceQuery();
  const [getQuestionAttemptStatusTimeline] = useLazyGetQuestionAttemptStatusTimelineServiceQuery();
  const [getErrorTopics] = useLazyGetErrorTopicsServiceQuery();
  const [getAiTutorFeedback] = useLazyGetAiTutorFeedbackServiceQuery();
  const [getAllQuestionsLog] = useLazyGetAllQuestionsLogServiceQuery();

  const dispatch = useDispatch();

  const ScoreOverview = useSelector((state) => state.studentDashboard.scoreOverview);
  const SubjectWiseScore = useSelector((state) => state.studentDashboard.subjectWiseScore);
  const AttemptBreakdown = useSelector((state) => state.studentDashboard.attemptBreakdown);
  const AccuracyBySubject = useSelector((state) => state.studentDashboard.accuracyBySubject);
  const TimeAccuracyTrends = useSelector((state) => state.studentDashboard.timeAccuracyTrends);
  const TimeSpentDistribution = useSelector(
    (state) => state.studentDashboard.timeSpentDistribution
  );
  const TimeJourney = useSelector((state) => state.studentDashboard.timeJourney);
  const QualityOfTime = useSelector((state) => state.studentDashboard.qualityOfTime);
  const LearningEfficiency = useSelector((state) => state.studentDashboard.learningEfficiency);
  const ConfusedAttempts = useSelector((state) => state.studentDashboard.confusedAttempts);
  const SubjectSwitchTimeline = useSelector(
    (state) => state.studentDashboard.subjectSwitchTimeline
  );
  const QuestionAttemptStatusTimeline = useSelector(
    (state) => state.studentDashboard.questionAttemptStatusTimeline
  );
  const ErrorTopics = useSelector((state) => state.studentDashboard.errorTopics);
  const AiTutorFeedback = useSelector((state) => state.studentDashboard.aiTutorFeedback);
  const AllQuestionsLog = useSelector((state) => state.studentDashboard.allQuestionsLog);

  console.log('Score Overview:', ScoreOverview);
  console.log('Subject Wise Score:', SubjectWiseScore);
  console.log('Attempt Breakdown:', AttemptBreakdown);
  console.log('Accuracy By Subject:', AccuracyBySubject);

  useEffect(() => {
    handleFetchScoreOverview();
    handleFetchSubjectWiseScore();
    handleFetchAttemptBreakdown();
    handleFetchAccuracyBySubject();
    handleFetchTimeAccuracyTrend();
    handleFetchTimeSpentDistribution();
    handleFetchTimeJourney();
    handleFetchQualityOfTime();
    handleFetchLearningEfficiency();
    handleFetchConfusedAttempts();
    handleFetchSubjectSwitchTimeline();
    handleFetchQuestionAttemptStatusTimeline();
    handleFetchErrorTopics();
    handleFetchAiTutorFeedback();
    handleFetchAllQuestionsLog();
  }, []);

  const handleFetchScoreOverview = async () => {
    try {
      const res = await getScoreOverview({ userId: sessionStorage.getItem('userId') }).unwrap();

      dispatch(setScoreOverview(res));
    } catch (error) {
      console.error('Error fetching score overview:', error);
    }
  };

  const handleFetchSubjectWiseScore = async () => {
    try {
      const res = await getSubjectWiseScore({ userId: sessionStorage.getItem('userId') }).unwrap();

      dispatch(setSubjectWiseScore(res));
    } catch (error) {
      console.error('Error fetching subject-wise score:', error);
    }
  };

  const handleFetchAttemptBreakdown = async () => {
    try {
      const res = await getAttemptBreakdown({ userId: sessionStorage.getItem('userId') }).unwrap();
      dispatch(setAttemptBreakdown(res));
    } catch (error) {
      console.error('Error fetching attempt breakdown:', error);
    }
  };

  const handleFetchAccuracyBySubject = async () => {
    try {
      const res = await getAccuracyBySubject({ userId: sessionStorage.getItem('userId') }).unwrap();
      dispatch(setAccuracyBySubject(res));
    } catch (error) {
      console.error('Error fetching accuracy by subject:', error);
    }
  };

  const handleFetchTimeAccuracyTrend = async () => {
    try {
      const res = await getTimeAccuracyTrend({ userId: sessionStorage.getItem('userId') }).unwrap();
      dispatch(setTimeAccuracyTrends(res));
    } catch (error) {
      console.error('Error fetching time accuracy trend:', error);
    }
  };

  const handleFetchTimeSpentDistribution = async () => {
    try {
      const res = await getTimeSpentDistribution({
        userId: sessionStorage.getItem('userId')
      }).unwrap();
      dispatch(setTimeSpentDistribution(res));
    } catch (error) {
      console.error('Error fetching time spent distribution:', error);
    }
  };

  const handleFetchTimeJourney = async () => {
    try {
      const res = await getTimeJourney({ userId: sessionStorage.getItem('userId') }).unwrap();
      dispatch(setTimeJourney(res));
    } catch (error) {
      console.error('Error fetching time journey:', error);
    }
  };

  const handleFetchQualityOfTime = async () => {
    try {
      const res = await getQualityOfTime({ userId: sessionStorage.getItem('userId') }).unwrap();
      dispatch(setQualityOfTime(res));
    } catch (error) {
      console.error('Error fetching quality of time:', error);
    }
  };

  const handleFetchLearningEfficiency = async () => {
    try {
      const res = await getLearningEfficiency({
        userId: sessionStorage.getItem('userId')
      }).unwrap();
      dispatch(setLearningEfficiency(res));
    } catch (error) {
      console.error('Error fetching learning efficiency:', error);
    }
  };

  const handleFetchConfusedAttempts = async () => {
    try {
      const res = await getConfusedAttempts({ userId: sessionStorage.getItem('userId') }).unwrap();
      dispatch(setConfusedAttempts(res));
    } catch (error) {
      console.error('Error fetching confused attempts:', error);
    }
  };

  const handleFetchSubjectSwitchTimeline = async () => {
    try {
      const res = await getSubjectSwitchTimeline({
        userId: sessionStorage.getItem('userId')
      }).unwrap();
      dispatch(setSubjectSwitchTimeline(res));
    } catch (error) {
      console.error('Error fetching subject switch timeline:', error);
    }
  };

  const handleFetchQuestionAttemptStatusTimeline = async () => {
    try {
      const res = await getQuestionAttemptStatusTimeline({
        userId: sessionStorage.getItem('userId')
      }).unwrap();
      dispatch(setQuestionAttemptStatusTimeline(res));
    } catch (error) {
      console.error('Error fetching question attempt status timeline:', error);
    }
  };

  const handleFetchErrorTopics = async () => {
    try {
      const res = await getErrorTopics({ userId: sessionStorage.getItem('userId') }).unwrap();
      dispatch(setErrorTopics(res));
    } catch (error) {
      console.error('Error fetching error topics:', error);
    }
  };

  const handleFetchAiTutorFeedback = async () => {
    try {
      const res = await getAiTutorFeedback({ userId: sessionStorage.getItem('userId') }).unwrap();
      dispatch(setAiTutorFeedback(res));
    } catch (error) {
      console.error('Error fetching AI tutor feedback:', error);
    }
  };

  const handleFetchAllQuestionsLog = async () => {
    try {
      const res = await getAllQuestionsLog({ userId: sessionStorage.getItem('userId') }).unwrap();
      dispatch(setAllQuestionsLog(res));
    } catch (error) {
      console.error('Error fetching all questions log:', error);
    }
  };

  const options = {
    chart: {
      type: 'bar' // 'bar' for horizontal; use 'column' for vertical
    },
    title: {
      text: 'Total fruit consumption, grouped by gender'
    },
    xAxis: {
      categories: ['Apples', 'Oranges', 'Pears', 'Grapes', 'Bananas']
    },
    yAxis: {
      min: 0,
      title: {
        text: 'Total fruit consumption'
      },
      stackLabels: {
        enabled: true,
        style: {
          fontWeight: 'bold',
          color: 'gray'
        }
      }
    },
    legend: {
      reversed: true
    },
    plotOptions: {
      series: {
        stacking: 'normal' // Use 'percent' for 100% stacked
      }
    },
    series: [
      {
        name: 'John',
        data: [5, 3, 4, 7, 2]
      },
      {
        name: 'Jane',
        data: [2, 2, 3, 2, 1]
      },
      {
        name: 'Joe',
        data: [3, 4, 4, 2, 5]
      }
    ]
  };

  const pieOptions = {
    chart: {
      type: 'pie',
    },
    title: {
      text: 'Browser market shares in January, 2025',
    },
    tooltip: {
      pointFormat: '{series.name}: <b>{point.percentage:.1f}%</b>',
    },
    accessibility: {
      point: {
        valueSuffix: '%',
      },
    },
    plotOptions: {
      pie: {
        allowPointSelect: true,
        cursor: 'pointer',
        dataLabels: {
          enabled: true,
          format: '<b>{point.name}</b>: {point.percentage:.1f} %',
        },
      },
    },
    series: [
      {
        name: 'Share',
        colorByPoint: true,
        data: [
          { name: 'Chrome', y: 63.1 },
          { name: 'Edge', y: 12.3 },
          { name: 'Firefox', y: 10.5 },
          { name: 'Safari', y: 8.4 },
          { name: 'Others', y: 5.7 },
        ],
      },
    ],
  };

  return (
    <div>
      <HighchartsReact highcharts={Highcharts} options={options} />;
      <HighchartsReact highcharts={Highcharts} options={pieOptions} />;
    </div>
  );
};

export default NewDashboard;
